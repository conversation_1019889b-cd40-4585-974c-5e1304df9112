<?php

namespace TF\Engine\Plugins\Core\Payments;

use Exception;
use TF\Engine\Kernel\Math;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbAreaTypes\UserDbAreaTypesController;
use TF\Engine\Plugins\Core\UserDbPayments\UserDbPaymentsController;

class PaymentsController extends UserDbController
{
    /**
     * @var PaymentsModel
     */
    public $DbHandler;
    public $Database;

    private $areaPrecision = 3;

    private $moneyPrecision = 2;

    private $rentaNatPrecision = 3;

    private $infinityPrecision = 16;

    private $fixMoneyRoundingValue = 0.1;

    private $personalUse = [];

    public function __construct($database, $params = [])
    {
        $this->DbHandler = new PaymentsModel($database);
        $this->Database = $database;

        if (isset($params['areaPrecision'])) {
            $this->areaPrecision = $params['areaPrecision'];
        }

        if (isset($params['moneyPrecision'])) {
            $this->moneyPrecision = $params['moneyPrecision'];
        }

        if (isset($params['rentaNatPrecision'])) {
            $this->rentaNatPrecision = $params['rentaNatPrecision'];
        }
    }

    public function getOwnersPayroll($year, $filterParams = [], $rows = null, $page = null, $returnFlatOwners = false)
    {
        $result = [];

        // Add filter by allowed farmings
        $allowedFarmingIds = $this->getAllowedFarmingIds();

        // Handle case when no allowed farming IDs are available
        if (empty($allowedFarmingIds)) {
            throw new Exception('User has no allowed farmings');
        }

        $filterParams['payroll_farming'] = !empty($filterParams['payroll_farming']) && '' != $filterParams['payroll_farming'][0]
            ? array_intersect($filterParams['payroll_farming'], $allowedFarmingIds)
            : $allowedFarmingIds;

        // Вземат се всички договори на база филтър параметрите
        $ownerContracts = $this->DbHandler->getOwnerContracts($year, $filterParams);

        // 1. Събираме всички owner_ids в масив Това е нужно заради страницирането. Страницирането се прави по собствениците, а не по договорите.
        $allOwnerIdsString = implode(',', array_column($ownerContracts, 'owner_ids'));
        $allOwnerIdsArray = array_unique(explode(',', $allOwnerIdsString));

        // Вземат се само собствениците, които са на желаната страница
        $start = ($page - 1) * $rows;
        $limitedOwners = array_slice($allOwnerIdsArray, $start, $rows);

        // Филтрират се договорите, които съдържат собственици от желаната страница
        $filteredContracts = array_filter($ownerContracts, function ($item) use ($limitedOwners) {
            $itemOwnerIds = explode(',', $item['owner_ids']);

            // Проверяваме дали поне едно от ID-тата в елемента е сред избраните собственици на база страницирането
            return count(array_intersect($itemOwnerIds, $limitedOwners)) > 0;
        });

        // След като се знаят собствениците на текущата страница, договорите в които участват се обхождат и се правят изчисленията за тях договорите
        // Нужно е изчисленията да се правят по договор, защото собствениците могат да имат наследници, да са плащали ренти за собствениците или наследниците и т.н.
        foreach ($filteredContracts as $contract) {
            $contractId = $contract['contract_id'];
            $contract['parent_ids'] = explode(',', $contract['parent_ids']) ?? null;
            $contract['owner_ids'] = explode(',', $contract['owner_ids']) ?? null;
            $annexId = null;

            if ($contract['parent_id']) {
                $contractId = $contract['parent_id'];
                $annexId = $contract['contract_id'];
            }

            // Строи се дървото на собственици за съответния договор. В нея се правят всички изчисления. Такива, каквито са в Ренти->Договори
            // Подават се и филтрите, защото може да имаме филтър по ЕКАТТЕ например и ще ни трябват данните за имоти само за съответното ЕКАТТЕ
            $contractsPayments = $this->getPayments($year, $contractId, $annexId, null, $filterParams);

            // Филтрираме собствениците и наследниците по owner_ids на съотвентния договор,
            // както и по owner_names, egn, heritor_names, heritor_egn
            $filterParams['owner_ids'] = $contract['owner_ids'];

            $filteredOwners = $this->filterOwnersTree($contractsPayments, $filterParams);

            // Във ведомостта данните за собствениците се агрегират по собственик.
            // Затова е нужно пресметнатите данни за собствениците се трансформират в двумерен масив, като първото ниво е по договор, а второто по path или id на собственика.
            $flatOwners[$contractId] = $this->flattenOwnersTree($filteredOwners);
        }

        // Return flat owners if requested, otherwise continue with normal processing
        if ($returnFlatOwners) {
            return [
                'rows' => $this->formattingOwnersData($flatOwners),
                'total' => count($flatOwners),
                'footer' => [],
            ];
        }

        // Сборуват се данните на всички собственици и техните наследници
        // Така ако собственик участва в два договора в $result ще има сборувани данните от двата договора
        foreach ($flatOwners as $contractId => $contractOwners) {
            foreach ($contractOwners as $ownerPath => $owner) {
                $result[$ownerPath] = $this->calculateOwnerData($result[$ownerPath], $owner);
            }
        }

        // След като сме сборували данните на всички собственици и техните наследници строим отново дървото, за да се покаже във Ведомостта
        $ownersTree = $this->buildOwnersTree($result);

        return [
            'rows' => $this->formattingOwnersData($ownersTree),
            'total' => count($allOwnerIdsArray),
            'footer' => [$this->generateFooter($ownersTree, ['timespan'])],
        ];
    }

    public function getOwnerPayroll($year, $ownerId = null, $path = null, $filterParams = [])
    {
        // Вземат се всички имоти за съответния договор заедно с рентите към тях.
        $plots = $this->getPaymentsPlots($year, null, null, $ownerId, $path, $filterParams);

        // Попълват се данните за личното ползване
        $plots = $this->processOwnerPersonalUse($plots);

        $aggPlots = $this->aggPlots($plots, 'rent_type');
        $aggPlotsByContract = $this->aggPlots($plots, 'contract');

        if (!isset($filterParams['no_contract_payments'])) {
            // Попълват се плащанията за собствениците по договори и се пресмятат оставащите ренти.
            $aggPlots = $this->contractsPaymentsMapping($aggPlots, $aggPlotsByContract, $year, $ownerId, $path);
        }

        // Форматираме данните за показване в грида
        return $this->formattingOwnersData($aggPlots);
    }

    public function contractsPaymentsMapping($aggPlots, $aggPlotsByContract, $year, $ownerId = null, $path = null)
    {
        foreach ($aggPlotsByContract as $contract) {
            $contractId = $contract['contract_id'];
            $annexId = null;
            $ownerPayment = null;
            if ($contract['parent_id']) {
                $contractId = $contract['parent_id'];
                $annexId = $contract['contract_id'];
            }

            $contractPayments = $this->getPayments($year, $contractId, $annexId);
            if ($contractPayments) {
                $ownerPayment = $this->getOwnerDataFromContract($contractPayments, $path ?? $ownerId);
            }
            foreach ($aggPlots as $plotKey => $plot) {
                if ($plot['contract_id'] == $contract['contract_id'] && $plot['parent_id'] == $contract['parent_id']) {
                    $aggPlots[$plotKey]['paid_renta'] = $ownerPayment['paid_renta'];
                    $aggPlots[$plotKey]['unpaid_renta'] = $ownerPayment['unpaid_renta'];
                    $aggPlots[$plotKey]['overpaid_renta'] = $ownerPayment['overpaid_renta'];
                    $aggPlots[$plotKey]['paid_renta_by'] = $ownerPayment['paid_renta_by'];
                    $aggPlots[$plotKey]['paid_nat_via_money'] = $ownerPayment['paid_nat_via_money'];
                    $aggPlots[$plotKey]['paid_nat_via_nat'] = $ownerPayment['paid_nat_via_nat'];
                    $aggPlots[$plotKey]['paid_renta_nat_sum'] = $ownerPayment['paid_renta_nat_sum'];
                    $aggPlots[$plotKey]['overpaid_renta_nat_money_arr'] = $ownerPayment['overpaid_renta_nat_money_arr'];
                    $aggPlots[$plotKey]['unpaid_renta_nat_money_arr'] = $ownerPayment['unpaid_renta_nat_money_arr'];
                    $aggPlots[$plotKey]['overpaid_renta_nat_arr'] = $ownerPayment['overpaid_renta_nat_arr'];
                    $aggPlots[$plotKey]['unpaid_renta_nat_arr'] = $ownerPayment['unpaid_renta_nat_arr'];
                    $aggPlots[$plotKey]['paid_via_nat'] = $ownerPayment['paid_via_nat'];
                    $aggPlots[$plotKey]['paid_via_money'] = $ownerPayment['paid_via_money'];
                    $aggPlots[$plotKey]['rent_place'] = strlen($aggPlots[$plotKey]['rent_place']) > 0 && '-' !== $aggPlots[$plotKey]['rent_place']
                        ? $aggPlots[$plotKey]['rent_place']
                        : $ownerPayment['rent_place_name'];
                }
            }
        }

        return $aggPlots;
    }

    public function getOwnerPayments($year, $ownerId)
    {
        if (empty($ownerId)) {
            return [
                'rows' => [],
                'total' => 0,
                'footer' => [],
            ];
        }

        $ownersPayments = [];
        $allowedFarmingIds = $this->getAllowedFarmingIds();

        // Handle case when no allowed farming IDs are available
        if (empty($allowedFarmingIds)) {
            throw new Exception('User has no allowed farmings');
        }

        // Вземат се всички договори на собственика за съответната година
        $ownerContracts = $this->DbHandler->getOwnerContracts($year, ['owner_id' => $ownerId, 'payroll_farming' => $allowedFarmingIds]);

        // Използва се методът getContractPayments(), защото в него се строи дървото на собствениците и се правят всички изчисления, включително и ако има платена рента
        // на собственика, тя се приспада от дължимите ренти на наследниците и обратно. За да се избегне по-сложна имплементация се ползват изчисленията от getContractPayments().
        foreach ($ownerContracts as $contract) {
            $contractId = $contract['contract_id'];
            $annexId = null;
            if ($contract['parent_id']) {
                $contractId = $contract['parent_id'];
                $annexId = $contract['contract_id'];
            }

            $contractsPayments = $this->getPayments($year, $contractId, $annexId);
            dd($contractsPayments);
            // След като се построи дървото със собственици и се направят изчисленията, се вземат данните само за съответния собственик.
            // Собственик може да участва няколко пъти в един договор, като наследник на различни собственици.
            // Затова рекурсивно се обхожда цялото дърво и се сумират стойностите за рентите и плащанията на собственика.
            if ($contractsPayments) {
                $ownersPayments[] = $this->sumOwnerDataInContract($contractsPayments, $ownerId);
            }
        }

        return [
            'rows' => $this->formattingOwnersData($ownersPayments),
            'total' => count($ownersPayments),
            'footer' => [$this->generateFooter($ownersPayments, ['timespan'])],
        ];
    }

    public function getContractPayments($year, $contractId = null, $annexId = null, $page = null, $rows = null, $sort = null, $order = null)
    {
        $payments = $this->getPayments($year, $contractId, $annexId, 'contract_payments');

        return [
            'rows' => $this->formattingOwnersData($payments),
            'total' => count($payments),
            'footer' => [
                $this->generateFooter($payments),
            ],
        ];
    }

    public function getPayments($year, $contractId = null, $annexId = null, $gridType = null, $filterParams = [])
    {
        // Вземат се всички имоти за съответния договор заедно с рентите към тях.
        $plots = $this->getPaymentsPlots($year, $contractId, $annexId, null, null, $filterParams);

        // Агрегират се данните по собственик
        $aggPlots = $this->aggPlots($plots);

        // Попълват се данните за личното ползване
        $aggPlots = $this->processOwnerPersonalUse($aggPlots, $gridType);

        // Вземат се плащанията за собствениците. Попълват се плащанията и се пресмятат оставащите ренти.
        // Тази операция се прави след агрегиране на данните, а не по имот, тъй като плащанията са по сосбтвеник.
        // При разбиване плащанията по имот и след това агрегиранията им се получават грешки при закръглянията.
        $contractAnnexId[] = $contractId;
        if (!empty($annexId)) {
            $contractAnnexId[] = $annexId;
        }
        $aggPlots = $this->processOwnerPayments($year, $aggPlots, $contractAnnexId);

        // Коригират се грешките от закръляния.
        $aggPlots = $this->fixRounding($aggPlots);

        // Filter children based on parent's dead status and farm year logic BEFORE building tree
        $aggPlots = $this->filterChildrenByParentDeadStatus($aggPlots);

        // Строим дървото на собствениците с наследниците
        $ownersTree = $this->buildOwnersTree($aggPlots);

        // Ако има плащания към собственика се приспадат дължимите ренти на наследниците.
        // Ако има плащания от даден наследник се приспада от дължимите ренти на собственика
        $this->calculateTreeRents($ownersTree);

        return $ownersTree;
    }

    public function fixRounding($payments)
    {
        foreach ($payments as $paymentKey => $payment) {
            // Заради закъглянията на площите може да се получи минимална разлика между дължимата и изплатена рента.
            // Затова тук се проверява дали има такава разлика и съответно се изважда или прибавя към дължимата рента.
            // Ако има начислена тогава се добавя към нея, в противен случай към рента по договор.
            $chargedRenta = $payment['charged_renta'] ?? 0;
            $renta = $payment['renta'] ?? 0;
            $paidRenta = $payment['paid_renta'] ?? 0;
            $sumRenta = Math::add($chargedRenta, $renta);
            $rentCal = Math::sub($sumRenta, $paidRenta);

            if ($rentCal > 0 && $rentCal <= $this->fixMoneyRoundingValue) {
                if ($chargedRenta > 0) {
                    $payments[$paymentKey]['charged_renta'] = Math::sub($chargedRenta, $rentCal);
                } else {
                    $payments[$paymentKey]['renta'] = Math::sub($renta, $rentCal);
                }

                $payments[$paymentKey]['unpaid_renta'] = Math::round(0, $this->moneyPrecision);
            }

            if ($rentCal < 0 && $rentCal >= ($this->fixMoneyRoundingValue * -1)) {
                if ($chargedRenta > 0) {
                    $payments[$paymentKey]['charged_renta'] = Math::sub($chargedRenta, $rentCal);
                } else {
                    $payments[$paymentKey]['renta'] = Math::sub($renta, $rentCal);
                }

                $payments[$paymentKey]['unpaid_renta'] = Math::round(0, $this->moneyPrecision);
            }
        }

        return $payments;
    }

    public function processOwnerPersonalUse($owners, $gridType = null)
    {
        $partCoef = 1;
        foreach ($owners as $ownerKey => $owner) {
            if (in_array($gridType, ['contract_payments'])) {
                $partCoef = Math::div($owner['plot_owned_area'], $owner['plot_owned_area_total']);
            }
            $owners[$ownerKey]['personal_use'] = [];
            $owners[$ownerKey]['personal_use_nat_type_id'] = [];
            $owners[$ownerKey]['personal_use_nat_types_names_arr'] = [];
            $owners[$ownerKey]['personal_use_amount_arr'] = [];
            $owners[$ownerKey]['personal_use_price_sum'] = [];
            $owners[$ownerKey]['personal_use_paid_arr'] = [];
            $owners[$ownerKey]['personal_use_unpaid_arr'] = [];
            $owners[$ownerKey]['personal_use_total'] = [];
            foreach ($this->personalUse as $personalUseValue) {
                if ($personalUseValue['owner_id'] === $owners[$ownerKey]['owner_id']) {
                    $owners[$ownerKey]['personal_use_nat_type_id'][] = $personalUseValue['renta_type'];
                    $owners[$ownerKey]['personal_use_unit_value'][] = $personalUseValue['personal_use_unit_value'];
                    $owners[$ownerKey]['personal_use_nat_types_names_arr'][] = $personalUseValue['renta_type_name'];
                    $owners[$ownerKey]['personal_use_renta_arr'][] = Math::mul($personalUseValue['personal_use_renta'], $partCoef, $this->moneyPrecision);
                    $owners[$ownerKey]['personal_use_paid_renta_arr'][] = Math::mul($personalUseValue['personal_use_paid_renta'], $partCoef, $this->moneyPrecision);
                    $owners[$ownerKey]['personal_use_unpaid_renta_arr'][] = Math::mul($personalUseValue['personal_use_unpaid_renta'], $partCoef, $this->moneyPrecision);
                    $owners[$ownerKey]['personal_use_treatments_sum_arr'][] = Math::mul($personalUseValue['personal_use_treatments_sum'], $partCoef, $this->moneyPrecision);
                    $owners[$ownerKey]['personal_use_paid_treatments_arr'][] = Math::mul($personalUseValue['personal_use_paid_treatments'], $partCoef, $this->moneyPrecision);
                    $owners[$ownerKey]['personal_use_unpaid_treatments_arr'][] = Math::mul($personalUseValue['personal_use_unpaid_treatments'], $partCoef, $this->moneyPrecision);
                }
            }
        }

        return $owners;
    }

    public function getPaymentsPlots($year, $contractId = null, $annexId = null, $ownerId = null, $path = null, $filterParams = [])
    {
        $contractAnnexId = !empty($annexId) ? $annexId : $contractId;
        $plots = $this->DbHandler->getPaymentPlots($year, $contractAnnexId, $ownerId, $path, $filterParams);

        $rentaNatArr = $this->DbHandler->getNatRents();

        // Взема се личното ползване за конкретните критерии
        $this->personalUse = $this->getPersonalUseForOwners([
            'contract_id' => $contractAnnexId,
            'year' => $year,
            'chosen_years' => $year,
        ], false, false);

        // Правят се всички изчисления за имота
        foreach ($plots as $plotKey => $plot) {
            // ******* Пресмятане на личното ползване за всеки имот - НАЧАЛО

            // Ако собственика е починал се проверява дали наследниците му имат лично ползване и ако има се добавя площта за лично ползване на наследниците в собственика
            if ($plot['is_dead']) {
                foreach ($plots as $PUplot) {
                    if ($PUplot['path']) {
                        $path = explode('.', $PUplot['path']);
                        if (in_array($plot['owner_id'], $path) && $PUplot['pc_rel_id'] == $plot['pc_rel_id']) {
                            $heritorPuArea = $this->getPersonalUseArea($this->personalUse, $PUplot['owner_id'], $PUplot);
                            if ($heritorPuArea) {
                                $plots[$plotKey]['pu_area'] = Math::add($plots[$plotKey]['pu_area'], $heritorPuArea, $this->areaPrecision);
                            }
                        }
                    }
                }
            } else {
                // Намира се площа за лично ползване за конкретния имот
                $plots[$plotKey]['pu_area'] = $this->getPersonalUseArea($this->personalUse, $plots[$plotKey]['owner_id'], $plots[$plotKey]);
            }
            // ******* Пресмятане на личното ползване за всеки имот - КРАЙ

            // calculation_area е площта, за която ще се правят изчисленията за рентите. Изважда се площта за лично ползване от общата площ на имота.
            // Ако трябва да се извадят други площи се прави тук.
            $plots[$plotKey]['calculation_area'] = Math::sub($plots[$plotKey]['plot_owned_area'], $plots[$plotKey]['pu_area'], $this->areaPrecision);

            // Това е цялата притежавана от собственика площ.
            $plots[$plotKey]['all_owner_area'] = Math::round($plots[$plotKey]['plot_owned_area'], $this->areaPrecision);
            $plots[$plotKey]['all_owner_contract_area'] = Math::round($plots[$plotKey]['plot_owned_contract_area'], $this->areaPrecision);
            $plots[$plotKey]['all_owner_no_rounded'] = $plots[$plotKey]['plot_owned_area'];
            $plots[$plotKey]['all_owner_no_rounded_contract'] = $plots[$plotKey]['plot_owned_contract_area'];
            $plots[$plotKey]['owner_area'] = Math::round($plots[$plotKey]['calculation_area'], $this->areaPrecision);

            // За един имот може да има повече от една рента в натура ПО ДОГОВОР. От заявката са взети всички ренти в натура ПО ДОГОВОРА за съотвенитя имот. Тук се парсва информацията за всяка рента в натура от договор.
            // $contractRents = json_decode($plot['contract_rents_values'], true);

            // За един имот може да има повече от една рента в натура ПО ДОГОВОР. От заявката са взети всички ренти в натура ПО ДОГОВОРА за съотвенитя имот. Тук се парсва информацията за всяка рента в натура от договор.
            // $rentaNatArr = json_decode($contractRents['rent_nature'], true);

            // За един имот може да има повече от една рента в натура ОТ НАЧИСЛЕНИЕ. От заявката са взети всички ренти в натура ОТ НАЧИСЛЕНИЕТО за съотвенитя имот. Тук се парсва информацията за всяка рента в натура от начисление.
            $chargedRentaNatArr = json_decode($plot['charged_renta_nat_json'], true);
            $rentaNatArr = json_decode($plot['renta_nat_json'], true);

            // Тук ще се съхнаряват id-тата на начислените натури, които ги има в договора. Тъй като може да има специфична рента в натура, която не същестува в договора
            // на база този масив ще се добавят и тези, които са от специфичната рента в натура
            $contractChargedRentaNat = [];

            // Обхождат се всички ренти в натура по договор и се правят изчисления за тях
            foreach ($rentaNatArr as $rentaNat) {
                $plots[$plotKey]['renta_nat'][$rentaNat['renta_nat_id']] = 0;
                if ($rentaNat['renta_nat_id']) {
                    $plots[$plotKey]['renta_nat_info'][$rentaNat['renta_nat_id']] = [
                        'renta_nat_id' => $rentaNat['renta_nat_id'],
                        'renta_nat_name' => $rentaNat['renta_nat_name'],
                        'unit_id' => $rentaNat['unit_id'],
                        'unit_name' => $GLOBALS['Contracts']['renta_units'][$rentaNat['unit_id']]['name'],
                        'nat_value' => $rentaNat['nat_value'],
                        'unit_value' => $rentaNat['unit_value'],
                        'charged_renta_nat' => null,
                        'contract_renta_nat' => null,
                    ];
                }

                $plots[$plotKey]['renta_nat_type'] .= $rentaNatArr[$rentaNat['renta_nat_id']]['renta_nat_name'] . ' (' . $GLOBALS['Contracts']['renta_units'][$rentaNatArr[$rentaNat['renta_nat_id']]['unit']]['renta_nat_name'] . ')<br>';

                if (!empty($chargedRentaNatArr)) {
                    // Проверява се дали за имота има начислена рента в натура. Ако има се взема тя, ако не, се взема рентата в натура по договор (в else)
                    foreach ($chargedRentaNatArr as $chargedRentaNat) {
                        // Ако има индивидуална рента за имота, не се прави начисление в натура
                        if ($plot['rent_per_plot_value'] > 0) {
                            continue;
                        }
                        if ($chargedRentaNat['charged_renta_nat_id'] == $rentaNat['id']) {
                            if ($chargedRentaNat['nat_is_converted']) { // Ако рентата в натура е преобразувана в пари
                                $chargedRentNatValue = Math::mul($chargedRentaNat['amount'], $chargedRentaNat['unit_value']);
                                $chargedRentaNatConverted = Math::mul($chargedRentNatValue, $plots[$plotKey]['calculation_area']);
                                $plots[$plotKey]['charged_renta_nat_converted'] = Math::add($plots[$plotKey]['charged_renta_nat_converted'] ?? 0, $chargedRentaNatConverted);
                            } else { // Ако имаме начисление на рента в натура
                                $plots[$plotKey]['charged_renta_nat'][$rentaNat['id']] = Math::mul($chargedRentaNat['amount'], $plots[$plotKey]['calculation_area']);
                                if ($plots[$plotKey]['renta_nat_info'][$rentaNat['id']]) {
                                    $plots[$plotKey]['renta_nat_info'][$rentaNat['id']]['charged_renta_nat'] = Math::mul($chargedRentaNat['amount'], $plots[$plotKey]['calculation_area']);
                                }
                            }

                            $contractChargedRentaNat[] = $rentaNat['renta_nat_id'];
                        } else {
                            // Намираме рентата в натура за всеки имот
                            $rentNatValue = $plot['rent_per_plot_value'] > 0 ? 0 : $rentaNat['nat_value']; // Ако за даден имот има индивидуална рента, тя се взема с най-голям приоритет и за този имот не се калкулира рента в натура
                            $plots[$plotKey]['renta_nat_info'][$rentaNat['renta_nat_id']]['contract_renta_nat'] = Math::mul($rentNatValue, $plots[$plotKey]['calculation_area']);

                            $plots[$plotKey]['renta_nat'][$rentaNat['renta_nat_id']] = Math::add($plots[$plotKey]['renta_nat'][$rentaNat['renta_nat_id']], $plots[$plotKey]['renta_nat_info'][$rentaNat['renta_nat_id']]['contract_renta_nat']);
                        }
                    }
                } else {
                    // Намираме рентата в натура за всеки имот
                    $rentNatValue = $plot['rent_per_plot_value'] > 0 ? 0 : $rentaNat['nat_value']; // Ако за даден имот има индивидуална рента, тя се взема с най-голям приоритет и за този имот не се калкулира рента в натура

                    if ($plots[$plotKey]['renta_nat_info'][$rentaNat['renta_nat_id']]) {
                        $plots[$plotKey]['renta_nat_info'][$rentaNat['renta_nat_id']]['contract_renta_nat'] = Math::mul($rentNatValue, $plots[$plotKey]['calculation_area']);
                    }

                    $plots[$plotKey]['renta_nat'][$rentaNat['renta_nat_id']] = Math::add($plots[$plotKey]['renta_nat'][$rentaNat['renta_nat_id']], $plots[$plotKey]['renta_nat_info'][$rentaNat['renta_nat_id']]['contract_renta_nat']);
                }

                // Обработване на начислената рента в натура, която идва от специфичните ренти, а не от договора.
                foreach ($chargedRentaNatArr as $chargedRentaNat) {
                    if (in_array($chargedRentaNat['renta_nat_id'], $contractChargedRentaNat) || $plot['rent_per_plot_value'] > 0) {
                        continue;
                    }

                    $rentNatUnitValue = 0;
                    foreach ($rentaNatArr as $rentaNatInner) {
                        if ($rentaNatInner['renta_nat_id'] == $chargedRentaNat['charged_renta_nat_id']) {
                            $rentNatUnitValue = $rentaNatInner['unit_value'];

                            break;
                        }
                    }

                    $plots[$plotKey]['renta_nat'][$chargedRentaNat['charged_renta_nat_id']] = 0;
                    if ($chargedRentaNat['charged_renta_nat_id']) {
                        $plots[$plotKey]['renta_nat_info'][$chargedRentaNat['charged_renta_nat_id']] = [
                            'renta_nat_id' => $chargedRentaNat['charged_renta_nat_id'],
                            'renta_nat_name' => $chargedRentaNat['charged_renta_nat_name'],
                            'unit_id' => $chargedRentaNat['unit_id'],
                            'unit_name' => $GLOBALS['Contracts']['renta_units'][$chargedRentaNat['unit_id']]['name'],
                            'nat_value' => null,
                            'unit_value' => $rentNatUnitValue,
                            'charged_renta_nat' => $chargedRentaNat['amount'],
                            'contract_renta_nat' => null,
                        ];

                        if ($chargedRentaNat['nat_is_converted']) { // Ако рентата в натура е преобразувана в пари
                            $chargedRentNatValue = Math::mul($chargedRentaNat['amount'], $chargedRentaNat['unit_value']);
                            $chargedRentaNatConverted = Math::mul($chargedRentNatValue, $plots[$plotKey]['calculation_area']);
                            $plots[$plotKey]['charged_renta_nat_converted'] = Math::add($plots[$plotKey]['charged_renta_nat_converted'] ?? 0, $chargedRentaNatConverted);
                        } else { // Ако имаме начисление на рента в натура
                            $plots[$plotKey]['charged_renta_nat'][$chargedRentaNat['charged_renta_nat_id']] = Math::mul($chargedRentaNat['amount'], $plots[$plotKey]['calculation_area']);
                            $plots[$plotKey]['renta_nat_info'][$chargedRentaNat['charged_renta_nat_id']]['charged_renta_nat'] = Math::mul($chargedRentaNat['amount'], $plots[$plotKey]['calculation_area']);
                        }
                    }
                }

                $plotCharedRentaNat = $plots[$plotKey]['renta_nat_info'][$rentaNat['renta_nat_id']]['charged_renta_nat'];
                $plotContractRentaNat = $plots[$plotKey]['renta_nat_info'][$rentaNat['renta_nat_id']]['contract_renta_nat'];

                // Оставаща рента в натура е рентата в натура по договор + начислена рента в натура.
                $unpaidRentaNat = Math::add($plotCharedRentaNat, $plotContractRentaNat);
                $plots[$plotKey]['unpaid_renta_nat_arr'][$rentaNat['renta_nat_id']] = $unpaidRentaNat;

                // Оставащата рента в натура конвертирана в пари се намира след като се умножи оставащата рента в натура по стойността на единицата на рентата в натура.
                $plots[$plotKey]['unpaid_renta_nat_money_arr'][$rentaNat['renta_nat_id']] = $unpaidRentaNat * $rentaNat['unit_value'];

                // Този масив се попълва в цикъла за плащанията. Тук се инициализира, за да може да имаме стойност за всяка рента в натура.
                // Тъй като плащанията могат да бъдат само в една натура. Ако не се инициализират останалите стойности в грида ще се разместят редовете.
                $plots[$plotKey]['overpaid_renta_nat_arr'][$rentaNat['renta_nat_id']] = Math::round(0, $this->rentaNatPrecision);

                // Този масив се попълва в цикъла за плащанията. Тук се инициализира, за да може да имаме стойност за всяка рента в натура.
                // Тъй като плащанията могат да бъдат само в една натура. Ако не се инициализират останалите стойности в грида ще се разместят редовете.
                $plots[$plotKey]['overpaid_renta_nat_money_arr'][$rentaNat['renta_nat_id']] = Math::round(0, $this->moneyPrecision);

                // Този масив се попълва в цикъла за плащанията. Тук се инициализира, за да може да имаме стойност за всяка рента в натура.
                // Тъй като плащанията могат да бъдат само в една натура. Ако не се инициализират останалите стойности в грида ще се разместят редовете.
                $plots[$plotKey]['paid_nat_via_nat'][$rentaNat['renta_nat_id']] = Math::round(0, $this->rentaNatPrecision);

                // Инициализират се всички ренти в натура. За да има стойности за всяка рента. Това е нужно за подреждането в грида.
                $plots[$plotKey]['paid_renta_nat_sum'][$rentaNat['renta_nat_id']] = Math::round(0, $this->rentaNatPrecision);
            }

            // Намиране на дължимата сума по договор
            // Ако имота има индивидуална рента се взема тя с най-голям приоритет
            // Ако имота няма индивидуална рента се взема рентата от начислението (ако има такова)
            // Ако няма индивидуална рента и начисление се взема рентата от договора/анекса
            if (null != $plot['rent_per_plot']) {
                $plots[$plotKey]['contract_renta'] = Math::mul($plot['rent_per_plot'], $plots[$plotKey]['calculation_area']);
                $plots[$plotKey]['renta'] = Math::mul($plot['rent_per_plot'], $plots[$plotKey]['calculation_area']);
            } elseif (null != $plots[$plotKey]['charged_renta_value'] || null != $plots[$plotKey]['charged_renta_nat_converted']) {
                $chargedRenta = Math::mul(($plots[$plotKey]['charged_renta_value'] ?? 0), $plots[$plotKey]['calculation_area']);
                $plots[$plotKey]['charged_renta'] = Math::add($chargedRenta, ($plots[$plotKey]['charged_renta_nat_converted'] ?? 0));
            } else {
                $plots[$plotKey]['contract_renta'] = Math::mul($plot['rent_money_value'], $plots[$plotKey]['calculation_area']);
                $plots[$plotKey]['renta'] = Math::mul($plot['rent_money_value'], $plots[$plotKey]['calculation_area']);
            }

            // // Плащанията са за собственик и договор, затова е нужно да се намери пропорцията на площта имота спрямо площта,
            // // която собственика притежава от договора, така ще може пропорционално да се разпредели плащането по имотите
            // $plots[$plotKey]['paid_renta'] = 0.00;

            // /Оставаща рента в пари е рентата в пари по договор + начислена рента в пари.
            $plots[$plotKey]['unpaid_renta'] = Math::add($plots[$plotKey]['contract_renta'], $plots[$plotKey]['charged_renta']);

            // Инициализира се стойността на надплатената рента.
            $plots[$plotKey]['overpaid_renta'] = Math::round(0, $this->moneyPrecision);
        }

        return $plots;
    }

    public function aggPlots($plots, $aggBy = 'owner')
    {
        $FarmingController = new FarmingController('Farming');

        // Агрегиране на данните по собственик
        $aggPlots = [];
        foreach ($plots as $key => $plot) {
            if ('owner' == $aggBy) {
                $aggKey = $plot['owner_id'] . '_' . ($plot['path'] ?? '0');
            } elseif ('plot' == $aggBy) {
                $aggKey = $plot['pc_rel_id'];
            } elseif ('contract' == $aggBy) {
                $aggKey = $plot['contract_id'];
            } elseif ('rent_type' == $aggBy) {
                $aggKey = $plot['owner_id'] . '_' . ($plot['plot_rent_type_id'] . '_' . $plot['pc_rel_id'] ?? '0');
            }

            if (!isset($aggPlots[$aggKey])) {
                $aggPlots[$aggKey] = [
                    'uuid' => mb_substr(md5($plot['owner_id'] . $plot['path']), 0, 6),
                    'contract_id' => $plot['contract_id'],
                    'parent_id' => $plot['parent_id'],
                    'c_num' => $plot['c_num'],
                    'c_num_with_group_name' => !empty($secondOwner['contract_group_name']) ? $plot['c_num'] . ' (' . $plot['contract_group_name'] . ')' : $plot['c_num'],
                    'contract_group_name' => $plot['contract_group_name'],
                    'egn_eik' => $plot['egn_eik'],
                    'farming_id' => $plot['farming_id'],
                    'farming_name' => $plot['farming_name'],
                    'contract_type' => $plot['contract_type'],
                    'contract_start_date' => $plot['contract_start_date'],
                    'contract_due_date' => $plot['contract_due_date'],
                    'osz_date' => $plot['osz_date'],
                    'osz_num' => $plot['osz_num'],
                    'sv_num' => $plot['sv_num'],
                    'sv_date' => $plot['sv_date'],
                    'annex_id' => $plot['annex_id'],
                    'id' => $key + 1, // DataGrid компонента в EasyUI изисква уникално ID за всеки ред
                    'owner_id' => $plot['owner_id'],
                    'owner_names' => $plot['owner_names'],
                    'owner_parent_names' => $plot['owner_parent_names'],
                    'owner_parent_id' => $plot['owner_parent_id'],
                    'phone' => $plot['phone'],
                    'mobile' => $plot['mobile'],
                    'rep_names' => $plot['rep_names'],
                    'rep_iban' => $plot['rep_iban'],
                    'rep_egn' => $plot['rep_egn'],
                    'rep_address' => $plot['rep_address'],
                    'rep_lk' => $plot['rep_lk'],
                    'rep_lk_izdavane' => $plot['rep_lk_izdavane'],
                    'address' => $plot['address'],
                    'lk_nomer' => $plot['lk_nomer'],
                    'lk_izdavane' => $plot['lk_izdavane'],
                    'owner_post_payment_fields' => $plot['owner_post_payment_fields'],
                    'iban' => $plot['iban'],
                    'rent_place_ekatte' => $plot['rent_place_ekatte'],
                    'rent_place_name' => $plot['rent_place_name'],
                    'is_dead' => $plot['is_dead'],
                    'dead_date' => $plot['dead_date'],
                    'allow_owner_payment' => $plot['allow_owner_payment'],
                    'dead_date_in_current_farm_year' => $plot['dead_date_in_current_farm_year'],
                    'dont_show_heritoris' => $plot['dont_show_heritoris'],
                    'is_heritor' => $plot['is_heritor'],
                    'plot_id' => $plot['plot_id'],
                    'kad_ident' => $plot['kad_ident'],
                    'ekatte_name' => $plot['ekatte_name'],
                    'area_type' => $plot['area_type'],
                    'mestnost' => $plot['mestnost'],
                    'category' => $plot['category'],
                    'pc_rel_id' => $plot['pc_rel_id'],
                    'plot_rent_type_id' => $plot['plot_rent_type_id'],
                    'plot_rent_type_category' => $plot['plot_rent_type_category'],
                    'plot_rent_type_value' => $plot['plot_rent_type_value'],
                    'plot_rent_type_title' => $plot['plot_rent_type_title'],
                    'path' => $plot['path'],
                    'owner_path_key' => $plot['owner_path_key'],
                    'all_owner_area' => $plot['all_owner_area'],
                    'all_owner_contract_area' => $plot['all_owner_contract_area'],
                    'all_owner_no_rounded' => $plot['all_owner_no_rounded'],
                    'all_owner_no_rounded_contract' => $plot['all_owner_no_rounded_contract'],
                    'plot_owned_area_total' => $plot['plot_owned_area_total'],
                    'plot_owned_area' => $plot['plot_owned_area'],
                    'pu_area' => $plot['pu_area'],
                    'owner_area' => $plot['calculation_area'],
                    'cultivated_area' => $plot['cultivated_area'],
                    'renta' => $plot['renta'],
                    'charged_renta' => $plot['charged_renta'],
                    'paid_renta' => Math::round(0, $this->moneyPrecision),
                    'paid_via_money' => Math::round(0, $this->moneyPrecision),
                    'unpaid_renta' => $plot['unpaid_renta'],
                    'overpaid_renta' => Math::round(0, $this->moneyPrecision),
                    'rent_per_plot_value' => $plot['rent_per_plot_value'],
                    'plot_rent' => $plot['plot_rent'],
                    'rent_money_value' => $plot['rent_money_value'],
                    'charged_renta_value' => $plot['charged_renta_value'],
                    'contracts_owned_area' => [
                        $plot['contract_id'] => $plot['contract_owned_area'],
                    ],
                ];

                foreach ($plot['renta_nat_info'] as $rentaNatInfo) {
                    $aggPlots[$aggKey]['renta_nat'][$rentaNatInfo['renta_nat_id']] = $rentaNatInfo['contract_renta_nat'];
                    $aggPlots[$aggKey]['charged_renta_nat'][$rentaNatInfo['renta_nat_id']] = $rentaNatInfo['charged_renta_nat'];

                    $aggPlots[$aggKey]['renta_nat_info'][$rentaNatInfo['renta_nat_id']] = [
                        'renta_nat_id' => $rentaNatInfo['renta_nat_id'],
                        'renta_nat_name' => $rentaNatInfo['renta_nat_name'],
                        'unit_id' => $rentaNatInfo['unit_id'],
                        'unit_value' => $rentaNatInfo['unit_value'],
                        'unit_name' => $GLOBALS['Contracts']['renta_units'][$rentaNatInfo['unit_id']]['name'],
                        'nat_value' => $rentaNatInfo['nat_value'],
                    ];
                }

                $aggPlots[$aggKey]['unpaid_renta_nat_arr'] = $plot['unpaid_renta_nat_arr'];
                $aggPlots[$aggKey]['overpaid_renta_nat_arr'] = $plot['overpaid_renta_nat_arr'];

                $aggPlots[$aggKey]['unpaid_renta_nat_money_arr'] = $plot['unpaid_renta_nat_money_arr'];
                $aggPlots[$aggKey]['overpaid_renta_nat_money_arr'] = $plot['overpaid_renta_nat_money_arr'];

                $farmingOptions = [
                    'return' => ['id', 'name', 'post_payment_fields'],
                    'where' => [
                        'group_id' => ['column' => 'group_id', 'compare' => '=', 'value' => $this->User->GroupID],
                        'id' => ['column' => 'id', 'compare' => '=', 'value' => $plot['farming_id']],
                    ],
                ];
                $farmings = $FarmingController->getFarmings($farmingOptions, false, false);
                $aggPlots[$aggKey]['farm_post_payment_fields'] = array_values($farmings)[0]['post_payment_fields'];
            } else {
                $aggPlots[$aggKey]['plot_id'][] = $plot['plot_id'];
                $aggPlots[$aggKey]['pc_rel_id'][] = $plot['pc_rel_id'];

                foreach ($plot['renta_nat_info'] as $rentaNatInfo) {
                    if (in_array($rentaNatInfo['renta_nat_id'], $aggPlots[$aggKey]['renta_nat_info'])) {
                        continue;
                    }

                    $aggPlots[$aggKey]['renta_nat_info'][$rentaNatInfo['renta_nat_id']] = [
                        'renta_nat_id' => $rentaNatInfo['renta_nat_id'],
                        'renta_nat_name' => $rentaNatInfo['renta_nat_name'],
                        'unit_id' => $rentaNatInfo['unit_id'],
                        'unit_value' => $rentaNatInfo['unit_value'],
                        'unit_name' => $GLOBALS['Contracts']['renta_units'][$rentaNatInfo['unit_id']]['name'],
                        'nat_value' => $rentaNatInfo['nat_value'],
                    ];
                }

                $aggPlots[$aggKey] = $this->sumPayments($aggPlots[$aggKey], $plot);
            }

            // При групиране по собственик $aggKey се повтаря за отделните парцели и съответно презаписва kad_ident както и останалите пропъртита на парцелите с тези на последния парцел.
            // Затова тук се добавят масиви за пропъртитата на парцела, които се ползват при експорт на ведомост.
            if (!is_array($aggPlots[$aggKey]['kad_idents_array'])) {
                $aggPlots[$aggKey]['kad_idents_array'] = [];
            }

            if (!in_array($plot['kad_ident'], $aggPlots[$aggKey]['kad_idents_array'])) {
                $aggPlots[$aggKey]['kad_idents_array'][] = $plot['kad_ident'];
            }

            if (!is_array($aggPlots[$aggKey]['rep_names_array'])) {
                $aggPlots[$aggKey]['rep_names_array'] = [];
            }

            if (strlen($plot['rep_names'] ?? '') > 0 && !in_array($plot['rep_names'], $aggPlots[$aggKey]['rep_names_array'])) {
                $aggPlots[$aggKey]['rep_names_array'][] = $plot['rep_names'];
            }

            if (!is_array($aggPlots[$aggKey]['rep_ibans_array'])) {
                $aggPlots[$aggKey]['rep_ibans_array'] = [];
            }

            if (strlen($plot['rep_iban'] ?? '') > 0 && !in_array($plot['rep_iban'], $aggPlots[$aggKey]['rep_ibans_array'])) {
                $aggPlots[$aggKey]['rep_ibans_array'][] = $plot['rep_iban'];
            }
        }

        return $aggPlots;
    }

    public function sumPayments($payment1, $payment2)
    {
        $payment1['plot_id'][] = $payment2['plot_id'];
        $payment1['pc_rel_id'][] = $payment2['pc_rel_id'];

        $payment1['all_owner_area'] = Math::add($payment1['all_owner_area'], $payment2['all_owner_area']);
        $payment1['all_owner_contract_area'] = Math::add($payment1['all_owner_contract_area'], $payment2['all_owner_contract_area']);
        $payment1['all_owner_no_rounded'] = Math::add($payment1['all_owner_no_rounded'], $payment2['all_owner_no_rounded']);
        $payment1['all_owner_no_rounded_contract'] = Math::add($payment1['all_owner_no_rounded_contract'], $payment2['all_owner_no_rounded_contract']);
        $payment1['pu_area'] = Math::add($payment1['pu_area'], $payment2['pu_area']);
        $payment1['owner_area'] = Math::add($payment1['owner_area'], $payment2['calculation_area']);
        $payment1['cultivated_area'] = Math::add($payment1['cultivated_area'], $payment2['cultivated_area']);
        $payment1['renta'] = Math::add($payment1['renta'], $payment2['renta']);
        $payment1['charged_renta'] = Math::add($payment1['charged_renta'], $payment2['charged_renta']);
        $payment1['unpaid_renta'] = Math::add($payment1['unpaid_renta'], $payment2['unpaid_renta']);

        $payment1['charged_renta_nat_values'][$payment2['plot_id']] = $payment2['charged_renta_nat'];
        $payment1['plots_percent'][$payment2['plot_id']] = $payment2['plots_percent'];

        foreach ($payment2['renta_nat_info'] as $rentaNatInfo) {
            $payment1['renta_nat'][$rentaNatInfo['renta_nat_id']] = Math::add($payment1['renta_nat'][$rentaNatInfo['renta_nat_id']], $rentaNatInfo['contract_renta_nat']);
            $payment1['renta_nat_names'][$rentaNatInfo['renta_nat_id']] = Math::add($payment1['renta_nat_names'][$rentaNatInfo['renta_nat_id']], $rentaNatInfo['charged_renta_nat']);
            $payment1['charged_renta_nat'][$rentaNatInfo['renta_nat_id']] = Math::add($payment1['charged_renta_nat'][$rentaNatInfo['renta_nat_id']], $rentaNatInfo['charged_renta_nat']);
        }

        foreach ($payment2['unpaid_renta_nat_arr'] as $unpaidRentaNat => $unpaidRentaNatValue) {
            $payment1['unpaid_renta_nat_arr'][$unpaidRentaNat] = Math::add($payment1['unpaid_renta_nat_arr'][$unpaidRentaNat], $unpaidRentaNatValue);
        }

        foreach ($payment2['unpaid_renta_nat_money_arr'] as $unpaidRentaNatMoney => $unpaidRentaNatMoneyValue) {
            $payment1['unpaid_renta_nat_money_arr'][$unpaidRentaNatMoney] = Math::add($payment1['unpaid_renta_nat_money_arr'][$unpaidRentaNatMoney], $unpaidRentaNatMoneyValue);
        }

        return $payment1;
    }

    public function processOwnerPayments($year, $owners, $contractAnnexIds = null, $ownerId = null, $aggKey = 'owner_path_key')
    {
        $UserDbController = new UserDbController($this->Database);

        // Вземат се плащанията за собствениците
        $paidResults = $this->getContractOwnerPayments($year, $contractAnnexIds, $ownerId);

        // Вземаме всички типове ренти. Нужни ще са ни при изчисляването на Оставащо количество в лева
        $rentNatsResults = $UserDbController->getItemsByParams(['tablename' => $UserDbController->DbHandler->tableRentaTypes], false, false);
        $rentNats = array_column($rentNatsResults, null, 'id');

        foreach ($owners as $ownerKey => $owner) {
            foreach ($paidResults as $paidResult) {
                // В базата на плащането се записва ID-то на договора, а не на анекса, затова ако има parent_id (id-то на договора) се взима то
                $contractAnnexId = $paidResult['parent_id'] ?? $paidResult['contract_id'];

                if ('owner_path_key' == $aggKey) {
                    $aggClause = $paidResult['owner_path_key'] == $owner['owner_path_key'];
                } elseif ('owner_id' == $aggKey) {
                    $aggClause = $paidResult['owner_id'] == $owner['owner_id'];
                }

                if ($aggClause && $paidResult['contract_id'] == $contractAnnexId) {
                    // Намира се сумата на плащането за собственика
                    $moneyTransAmount = $paidResult['trans_amount'];

                    // Намира се сумата на плащането в натура за собственика
                    $natTransAmount = $paidResult['trans_amount_nat'];

                    // Попълва се информацията за плащането
                    if (1 == $paidResult['paid_in'] && 1 == $paidResult['paid_from']) { // Плащане на пари в пари
                        $owners[$ownerKey]['paid_renta'] = Math::add($owners[$ownerKey]['paid_renta'], $moneyTransAmount);
                        $owners[$ownerKey]['unpaid_renta'] = Math::sub($owners[$ownerKey]['unpaid_renta'], $moneyTransAmount);
                        $owners[$ownerKey]['paid_via_money'] = Math::add($owners[$ownerKey]['paid_via_money'], $moneyTransAmount);
                    } elseif (2 == $paidResult['paid_in'] && 1 == $paidResult['paid_from']) { // Плащане на пари в натура
                        // Ако има плащане на пари в натура с еднаква цена то количествата се сумират и се показват на един ред
                        // Ако обаче е платена една и съща натура с различна цена се показват на различни редове
                        $rentaNatKey = $paidResult['nat_type'] . '_' . $paidResult['unit_value'];
                        if (!isset($owners[$ownerKey]['paid_via_nat'][$rentaNatKey])) {
                            $owners[$ownerKey]['paid_via_nat'][$rentaNatKey] = [
                                'nat_trans_amount' => $natTransAmount,
                                'nat_unit_value' => $paidResult['unit_value'],
                                'nat_trans_type_unit_name' => $GLOBALS['Contracts']['renta_units'][$paidResult['trans_nat_type_unit']]['name'],
                                'nat_type_id' => $paidResult['nat_type'],
                                'trans_nat_type_text' => $paidResult['trans_nat_type_text'],
                            ];
                        } else {
                            $owners[$ownerKey]['paid_via_nat'][$rentaNatKey]['nat_trans_amount'] = Math::add($owners[$ownerKey]['paid_via_nat'][$rentaNatKey]['nat_trans_amount'], $natTransAmount);
                        }

                        // Изплатена рента в пари
                        $owners[$ownerKey]['paid_renta'] = Math::add($owners[$ownerKey]['paid_renta'], $moneyTransAmount);

                        // Оставаща рента в пари
                        $owners[$ownerKey]['unpaid_renta'] = Math::sub($owners[$ownerKey]['unpaid_renta'], $moneyTransAmount);
                    } elseif (1 == $paidResult['paid_in'] && 2 == $paidResult['paid_from']) { // Плащане на натура в пари
                        if (!isset($owners[$ownerKey]['paid_nat_via_money'][$paidResult['nat_type']]['nat_trans_amount'])) {
                            $owners[$ownerKey]['paid_nat_via_money'][$paidResult['nat_type']] = [
                                'nat_trans_amount' => $natTransAmount,
                                'nat_unit_value' => $paidResult['unit_value'],
                                'nat_trans_type_unit_name' => $GLOBALS['Contracts']['renta_units'][$paidResult['trans_nat_type_unit']]['name'],
                                'nat_type_id' => $paidResult['nat_type'],
                                'trans_nat_type_text' => $paidResult['trans_nat_type_text'],
                            ];
                        } else {
                            $owners[$ownerKey]['paid_nat_via_money'][$paidResult['nat_type']]['nat_trans_amount'] = Math::add($owners[$ownerKey]['paid_nat_via_money'][$paidResult['nat_type']]['nat_trans_amount'], $natTransAmount);
                        }

                        $owners[$ownerKey]['paid_renta_nat_sum'][$paidResult['nat_type']] = Math::add($owners[$ownerKey]['paid_renta_nat_sum'][$paidResult['nat_type']], $natTransAmount);

                        $owners[$ownerKey]['unpaid_renta_nat_arr'][$paidResult['nat_type']] = Math::sub($owners[$ownerKey]['unpaid_renta_nat_arr'][$paidResult['nat_type']], $natTransAmount);

                        // Намираме оставащото количество в лева като умножим количеството по дефолтната цена на натурата
                        $natTransAmountValue = Math::mul($natTransAmount, $rentNats[$paidResult['nat_type']]['unit_value']);
                        $owners[$ownerKey]['unpaid_renta_nat_money_arr'][$paidResult['nat_type']] = Math::sub($owners[$ownerKey]['unpaid_renta_nat_money_arr'][$paidResult['nat_type']], $natTransAmountValue);
                    } elseif (2 == $paidResult['paid_in'] && 2 == $paidResult['paid_from']) {  // Плащане на натура в натура
                        if (!isset($owners[$ownerKey]['paid_nat_via_nat'][$paidResult['nat_type']]['nat_trans_amount'])) {
                            $owners[$ownerKey]['paid_nat_via_nat'][$paidResult['nat_type']] = [
                                'nat_trans_amount' => $natTransAmount,
                                'nat_unit_value' => $paidResult['unit_value'],
                                'nat_trans_type_unit_name' => $GLOBALS['Contracts']['renta_units'][$paidResult['trans_nat_type_unit']]['name'],
                                'nat_type_id' => $paidResult['nat_type'],
                                'trans_nat_type_text' => $paidResult['trans_nat_type_text'],
                            ];
                        } else {
                            $owners[$ownerKey]['paid_nat_via_nat'][$paidResult['nat_type']]['nat_trans_amount'] = Math::add($owners[$ownerKey]['paid_nat_via_nat'][$paidResult['nat_type']]['nat_trans_amount'], $natTransAmount);
                        }

                        $monetyTranseAmountValue = Math::div($moneyTransAmount, $paidResult['unit_value']);
                        $owners[$ownerKey]['paid_renta_nat_sum'][$paidResult['nat_type']] = Math::add($owners[$ownerKey]['paid_renta_nat_sum'][$paidResult['nat_type']], $monetyTranseAmountValue);

                        $owners[$ownerKey]['unpaid_renta_nat_arr'][$paidResult['nat_type']] = Math::sub($owners[$ownerKey]['unpaid_renta_nat_arr'][$paidResult['nat_type']], $natTransAmount);

                        // Намираме оставащото количество в лева като умножим количеството по дефолтната цена на натурата
                        $natTransAmountValue = Math::mul($natTransAmount, $rentNats[$paidResult['nat_type']]['unit_value']);
                        $owners[$ownerKey]['unpaid_renta_nat_money_arr'][$paidResult['nat_type']] = Math::sub($owners[$ownerKey]['unpaid_renta_nat_money_arr'][$paidResult['nat_type']], $natTransAmountValue);
                    }
                }
            }
        }

        return $owners;
    }

    public function buildOwnersTree(array $owners, array $tree = [], int $level = 1): array
    {
        foreach ($owners as $ownerKey => $owner) {
            if (empty($owner['path'])) {
                $tree[$owner['owner_id']] = $owner;
                $tree[$owner['owner_id']]['iconCls'] = 'icon-tree-user';
                $tree[$owner['owner_id']]['level'] = $level;

                if ($owner['is_dead']) {
                    $tree[$owner['owner_id']]['owner_names'] .= ' - починал на: ' . date('d.m.Y г.', strtotime($owner['dead_date']));
                    $tree[$owner['owner_id']]['iconCls'] = 'icon-tree-user-rip';
                }

                unset($owners[$ownerKey]);
            } else {
                $pathParts = explode('.', $owner['path']);
                if (count($pathParts) > $level) {
                    continue;
                }

                array_pop($pathParts);

                $key = array_shift($pathParts);
                $currentTree = &$tree;

                while (null !== $key) {
                    if (!isset($currentTree[$key])) {
                        // Ако ключът не съществува, пропускаме
                        break;
                    }

                    if (empty($pathParts)) {
                        // Ако няма повече елементи в пътя, актуализираме стойността
                        $currentTree[$key]['children'][$owner['owner_id']] = $owner;

                        if (!empty($currentTree[$key]['dead_date']) && 1 == count($currentTree[$key]['children']) && false === strpos($currentTree[$key]['owner_names'], '- починал на:')) {
                            $currentTree[$key]['owner_names'] .= ' - починал на: ' . date('d.m.Y г.', strtotime($currentTree[$key]['dead_date']));
                        }

                        if (!empty($currentTree[$key]['children'][$owner['owner_id']]['dead_date']) && true == $currentTree[$key]['children'][$owner['owner_id']]['is_dead']) {
                            $currentTree[$key]['children'][$owner['owner_id']]['owner_names'] .= ' - починал на: ' . date('d.m.Y г.', strtotime($currentTree[$key]['children'][$owner['owner_id']]['dead_date']));
                        }

                        $currentTree[$key]['children'][$owner['owner_id']]['heritor_percent'] = Math::div($owner['all_owner_area'], $currentTree[$key]['all_owner_area']);
                        $currentTree[$key]['children'][$owner['owner_id']]['heritor_percent'] = Math::div($owner['all_owner_contract_area'], $currentTree[$key]['all_owner_contract_area']);
                        $currentTree[$key]['children'][$owner['owner_id']]['heritor_no_rounded'] = Math::div($owner['all_owner_no_rounded'], $currentTree[$key]['all_owner_no_rounded']);
                        $currentTree[$key]['children'][$owner['owner_id']]['heritor_percent'] = Math::div($owner['all_owner_no_rounded_contract'], $currentTree[$key]['all_owner_no_rounded_contract']);
                        $currentTree[$key]['children'][$owner['owner_id']]['level'] = $level;

                        if (true == $currentTree[$key]['children'][$owner['owner_id']]['is_dead']) {
                            $currentTree[$key]['children'][$owner['owner_id']]['iconCls'] = 'icon-tree-user-rip';
                        } else {
                            $currentTree[$key]['children'][$owner['owner_id']]['iconCls'] = 'icon-tree-user';
                        }
                        // Има ли платени ренти на наследниците. Тук се инициализира стойността, а в calculateParentRentsBasedOfChildrenPayments() се попълва
                        $currentTree[$key]['have_children_paids'] = false;

                        break;
                    }
                    if (!isset($currentTree[$key]['children'])) {
                        $currentTree[$key]['children'] = [];
                    }
                    $currentTree = &$currentTree[$key]['children'];

                    $key = array_shift($pathParts);
                }

                unset($owners[$ownerKey]);
            }
        }

        if (!empty($owners)) {
            $tree = $this->buildOwnersTree($owners, $tree, $level + 1);
        }

        return $tree;
    }

    public function formattingOwnersData($aggPlots)
    {
        $UserDbAreaTypesController = new UserDbAreaTypesController($this->Database);
        foreach ($aggPlots as $aggPlotKey => $aggPlot) {
            if (0 == $aggPlot['pu_area']) {
                $aggPlots[$aggPlotKey]['pu_area'] = '-';
            }

            // Datagrid-a в EasyUI изисква уникално ID за всеки ред
            $aggPlots[$aggPlotKey]['id'] = $aggPlots[$aggPlotKey]['uuid'];

            // FE на TF очаква contract_id-то да е на главния договор, затова ако има parent_id (id-то на договора) се взима то
            if ($aggPlots[$aggPlotKey]['parent_id']) {
                $aggPlots[$aggPlotKey]['annex_id'] = $aggPlots[$aggPlotKey]['contract_id'];
                $aggPlots[$aggPlotKey]['contract_id'] = $aggPlots[$aggPlotKey]['parent_id'];
            }

            $aggPlots[$aggPlotKey]['plot_rent'] = number_format($aggPlots[$aggPlotKey]['rent_per_plot_value'] ?? $aggPlots[$aggPlotKey]['charged_renta_value'] ?? $aggPlots[$aggPlotKey]['rent_money_value'], 2, '.', '');
            $aggPlots[$aggPlotKey]['plot_rent_txt'] = BGNtoEURO($aggPlots[$aggPlotKey]['plot_rent'], $this->moneyPrecision);

            if (empty($aggPlots[$aggPlotKey]['rent_place_name'])) {
                $aggPlots[$aggPlotKey]['rent_place_name'] = '-';
            }

            if (empty($aggPlots[$aggPlotKey]['category'])) {
                $aggPlots[$aggPlotKey]['category'] = '-';
            }

            if (empty($aggPlots[$aggPlotKey]['osz_num'])) {
                $aggPlots[$aggPlotKey]['osz_num'] = '-';
            }

            if (empty($aggPlots[$aggPlotKey]['osz_date'])) {
                $aggPlots[$aggPlotKey]['osz_date'] = '-';
            }

            if (empty($aggPlots[$aggPlotKey]['charged_renta']) || 0 == $aggPlots[$aggPlotKey]['charged_renta']) {
                $aggPlots[$aggPlotKey]['charged_renta'] = '-';
            }

            if (empty($aggPlots[$aggPlotKey]['pu_area'])) {
                $aggPlots[$aggPlotKey]['pu_area'] = '-';
            }

            if (empty($aggPlots[$aggPlotKey]['renta_nat_type'])) {
                $aggPlots[$aggPlotKey]['renta_nat_type'] = '[Без рента в натура]';
            }

            if (!empty($aggPlots[$aggPlotKey]['plot_rent_type_category'])) {
                $plotRentTypeValueText = $aggPlots[$aggPlotKey]['plot_rent_type_title'] . ':  ' . $aggPlots[$aggPlotKey]['plot_rent_type_value'];
                if ('arable' == $aggPlots[$aggPlotKey]['plot_rent_type_category']) {
                    $plotRentTypeValueText = ('true' == $aggPlots[$aggPlotKey]['plot_rent_type_value']) ? 'Обработваема земя' : 'Необработваема земя';
                } elseif ('ntp' == $aggPlots[$aggPlotKey]['plot_rent_type_category']) {
                    $plotRentTypeValueText = $aggPlots[$aggPlotKey]['plot_rent_type_title'] . ':  ' . $aggPlots[$aggPlotKey]['plot_rent_type_value'] . ' (' . $UserDbAreaTypesController->getNtpTitle($aggPlots[$aggPlotKey]['plot_rent_type_value']) . ')';
                }
                $aggPlots[$aggPlotKey]['plot_rent_type_text'] = $plotRentTypeValueText;
            }

            // Проверяваме дали някоя оставащите ренти е по-малка от 0. Ако е по-малка от 0, това означава, че е надплатена.
            // Зануляваме стойността на не платената рента и записваме абсолютната стойност за надплатена рента.
            if ($aggPlots[$aggPlotKey]['unpaid_renta'] < 0) {
                $aggPlots[$aggPlotKey]['overpaid_renta'] = abs($aggPlots[$aggPlotKey]['unpaid_renta']);
                $aggPlots[$aggPlotKey]['unpaid_renta'] = number_format(0, $this->moneyPrecision, '.', '');
            }

            if (!empty($aggPlots[$aggPlotKey]['unpaid_renta_nat_arr'])) {
                foreach ($aggPlots[$aggPlotKey]['unpaid_renta_nat_arr'] as $rentaNatId => $unpaidRentaNat) {
                    if ($unpaidRentaNat < 0) {
                        $aggPlots[$aggPlotKey]['overpaid_renta_nat_arr'][$rentaNatId] = abs($unpaidRentaNat);
                        $aggPlots[$aggPlotKey]['unpaid_renta_nat_arr'][$rentaNatId] = Math::round(0, $this->rentaNatPrecision);
                        $aggPlots[$aggPlotKey]['unpaid_renta_nat_money_arr'][$rentaNatId] = Math::round(0, $this->moneyPrecision);
                    }
                }
            }

            if (!empty($aggPlots[$aggPlotKey]['unpaid_renta_nat_money_arr'])) {
                foreach ($aggPlots[$aggPlotKey]['unpaid_renta_nat_money_arr'] as $rentaNatId => $unpaidRentaNatMoney) {
                    if ($unpaidRentaNatMoney < 0) {
                        $aggPlots[$aggPlotKey]['overpaid_renta_nat_money_arr'][$rentaNatId] = abs($unpaidRentaNatMoney);
                        $aggPlots[$aggPlotKey]['unpaid_renta_nat_money_arr'][$rentaNatId] = Math::round(0, $this->moneyPrecision);
                    }
                }
            }

            // $hasRentaNat = false;
            if (!empty($aggPlots[$aggPlotKey]['renta_nat'])) {
                ksort($aggPlots[$aggPlotKey]['renta_nat']);
                // Зануляваме стойностите на рентите, за да може колкото и пъти да извикаме този метод, да не се натрупват стойностите
                $aggPlots[$aggPlotKey]['renta_nat_with_type'] = '';
                $aggPlots[$aggPlotKey]['all_renta_nat_with_type'] = '';
                $aggPlots[$aggPlotKey]['renta_nat_text'] = implode('<br>', array_map(function ($rentaNat) {
                    return number_format($rentaNat, $this->rentaNatPrecision, '.', '');
                }, $aggPlots[$aggPlotKey]['renta_nat']));

                $aggPlots[$aggPlotKey]['renta_nat'] = array_map(function ($rentaNat) {
                    return number_format($rentaNat, $this->rentaNatPrecision, '.', '');
                }, $aggPlots[$aggPlotKey]['renta_nat']);

                foreach ($aggPlots[$aggPlotKey]['renta_nat'] as $natType => $natValue) {
                    $aggPlots[$aggPlotKey]['renta_nat_with_type'] .= number_format($natValue, $this->rentaNatPrecision, '.', '') . ' X ' . $aggPlots[$aggPlotKey]['renta_nat_info'][$natType]['renta_nat_name'] . '(' . $aggPlots[$aggPlotKey]['renta_nat_info'][$natType]['unit_name'] . ')<br>';

                    $chargedRentNat = $aggPlot['charged_renta_nat'][$natType] ?? 0;
                    $aggPlots[$aggPlotKey]['all_renta_nat_with_type'] .= number_format($natValue + $chargedRentNat, $this->rentaNatPrecision, '.', '') . ' X ' . $aggPlots[$aggPlotKey]['renta_nat_info'][$natType]['renta_nat_name'] . '(' . $aggPlots[$aggPlotKey]['renta_nat_info'][$natType]['unit_name'] . ')<br>';
                }
                $aggPlots[$aggPlotKey]['renta_nat_with_type'] = rtrim($aggPlots[$aggPlotKey]['renta_nat_with_type'], '<br>');

                ksort($aggPlots[$aggPlotKey]['renta_nat_info']);
                $aggPlots[$aggPlotKey]['renta_nat_type'] = implode('<br>', array_map(function ($rentaNat) {
                    return $rentaNat['renta_nat_name'] . ' (' . $rentaNat['unit_name'] . ')';
                }, $aggPlots[$aggPlotKey]['renta_nat_info']));
            } else {
                $aggPlots[$aggPlotKey]['renta_nat'] = [];
                $aggPlots[$aggPlotKey]['renta_nat_text'] = '-';
            }

            $hasChargedRentaNat = false;

            if (!empty($aggPlots[$aggPlotKey]['charged_renta_nat'])) {
                $aggPlots[$aggPlotKey]['charged_renta_nat_with_type'] = '';
                ksort($aggPlots[$aggPlotKey]['charged_renta_nat']);
                $aggPlots[$aggPlotKey]['charged_renta_nat_text'] = implode('<br>', array_map(function ($chargedRentaNat) {
                    if (is_numeric($chargedRentaNat)) {
                        return number_format($chargedRentaNat, $this->rentaNatPrecision, '.', '');
                    }

                    return '-';
                }, $aggPlots[$aggPlotKey]['charged_renta_nat']));

                $aggPlots[$aggPlotKey]['charged_renta_nat'] = array_map(function ($chargedRentaNat) {
                    if (is_numeric($chargedRentaNat)) {
                        return number_format($chargedRentaNat, $this->rentaNatPrecision, '.', '');
                    }

                    return;
                }, $aggPlots[$aggPlotKey]['charged_renta_nat']);

                foreach ($aggPlots[$aggPlotKey]['charged_renta_nat'] as $natType => $natValue) {
                    if ($natValue > 0 && !$hasChargedRentaNat) {
                        $hasChargedRentaNat = true;
                    }
                    $aggPlots[$aggPlotKey]['charged_renta_nat_with_type'] .= number_format($natValue, $this->rentaNatPrecision, '.', '') . ' X ' . $aggPlots[$aggPlotKey]['renta_nat_info'][$natType]['renta_nat_name'] . '(' . $aggPlots[$aggPlotKey]['renta_nat_info'][$natType]['unit_name'] . ')<br>';
                }
            }

            if (!$hasChargedRentaNat) {
                $aggPlots[$aggPlotKey]['charged_renta_nat_with_type'] = [];
                $aggPlots[$aggPlotKey]['charged_renta_nat_text'] = '-';
            }

            // Форматиращата функция може да се викне повече от веднъж. Зануляваме стоиностите на стринговите с натрупване, за да избегнем дублирането на записи
            if (!empty($aggPlots[$aggPlotKey]['paid_renta_by'])) {
                $aggPlots[$aggPlotKey]['paid_renta_by'] = '';
                $aggPlots[$aggPlotKey]['paid_renta_by_txt'] = '';
            }

            if (!$aggPlots[$aggPlotKey]['paid_renta_by_arr']) {
                // This array is needed for payroll xls export
                $aggPlots[$aggPlotKey]['paid_renta_by_arr'] = [
                    'amount' => 0,
                    'nat_amount' => [],
                ];
            }

            if (!empty($aggPlots[$aggPlotKey]['paid_via_money'])) {
                $aggPlots[$aggPlotKey]['paid_renta_by'] = BGNtoEURO($aggPlots[$aggPlotKey]['paid_via_money'], $this->moneyPrecision) . '<br>';
                $aggPlots[$aggPlotKey]['paid_renta_by_txt'] = BGNtoEURO($aggPlots[$aggPlotKey]['paid_via_money'], $this->moneyPrecision) . '<br>';
                $aggPlots[$aggPlotKey]['paid_renta_by_arr']['amount'] = $aggPlots[$aggPlotKey]['paid_renta_by'];
            }

            if (!empty($aggPlots[$aggPlotKey]['paid_via_nat'])) {
                foreach ($aggPlots[$aggPlotKey]['paid_via_nat'] as $aggPlotPatViaNat) {
                    $aggPlots[$aggPlotKey]['paid_renta_by'] .= number_format($aggPlotPatViaNat['nat_trans_amount'], $this->rentaNatPrecision, '.', '') . ' ' . $aggPlotPatViaNat['nat_trans_type_unit_name'] . ' ' . $aggPlotPatViaNat['trans_nat_type_text'] . ' Х ' . BGNtoEURO($aggPlotPatViaNat['nat_unit_value'], $this->moneyPrecision) . '<br>';
                    $aggPlots[$aggPlotKey]['paid_renta_by_txt'] .= number_format($aggPlotPatViaNat['nat_trans_amount'], $this->rentaNatPrecision, '.', '') . ' ' . $aggPlotPatViaNat['nat_trans_type_unit_name'] . ' ' . $aggPlotPatViaNat['trans_nat_type_text'] . ' Х ' . BGNtoEURO($aggPlotPatViaNat['nat_unit_value'], $this->moneyPrecision) . '<br>';
                    $aggPlots[$aggPlotKey]['paid_renta_by_arr']['nat_amount'][$aggPlotPatViaNat['nat_type_id']] = number_format($aggPlotPatViaNat['nat_trans_amount'], $this->rentaNatPrecision, '.', '') . '(' . $aggPlotPatViaNat['nat_trans_type_unit_name'] . ')';
                }
            }

            if (empty($aggPlots[$aggPlotKey]['paid_renta_by'])) {
                $aggPlots[$aggPlotKey]['paid_renta_by'] = '-';
            }

            if (empty($aggPlots[$aggPlotKey]['paid_renta_by_txt'])) {
                $aggPlots[$aggPlotKey]['paid_renta_by_txt'] = '-';
            }

            if (!empty($aggPlots[$aggPlotKey]['paid_renta_nat_sum'])) {
                ksort($aggPlots[$aggPlotKey]['paid_renta_nat_sum']);
                $aggPlots[$aggPlotKey]['paid_renta_nat'] = '';
                $aggPlots[$aggPlotKey]['paid_renta_nat_with_type'] = '';
                foreach ($aggPlots[$aggPlotKey]['paid_renta_nat_sum'] as $natType => $paidRentaNat) {
                    $aggPlots[$aggPlotKey]['paid_renta_nat'] .= number_format($paidRentaNat, $this->rentaNatPrecision, '.', '') . '<br>';
                    $aggPlots[$aggPlotKey]['paid_renta_nat_with_type'] .= number_format($paidRentaNat, $this->rentaNatPrecision, '.', '') . ' X ' . $aggPlots[$aggPlotKey]['renta_nat_info'][$natType]['renta_nat_name'] . '(' . $aggPlots[$aggPlotKey]['renta_nat_info'][$natType]['unit_name'] . ')<br>';
                    $aggPlots[$aggPlotKey]['paid_renta_nat_arr'][$natType] = $paidRentaNat;
                }
                rtrim($aggPlots[$aggPlotKey]['paid_renta_nat_with_type'], '<br>');
            } else {
                $aggPlots[$aggPlotKey]['paid_renta_nat'] = implode('<br>', array_map(function ($rentaNat) {
                    return number_format(0, $this->rentaNatPrecision, '.', '');
                }, $aggPlots[$aggPlotKey]['renta_nat_info']));
            }

            if (empty($aggPlots[$aggPlotKey]['paid_renta_nat'])) {
                $aggPlots[$aggPlotKey]['paid_renta_nat'] = '-';
            }

            if (empty($aggPlots[$aggPlotKey]['paid_renta_nat_arr'])) {
                $aggPlots[$aggPlotKey]['paid_renta_nat_arr'] = [];
            }

            $hasPaidRenta = false;
            $aggPlots[$aggPlotKey]['paid_renta_nat_by_detailed'] = '';
            $aggPlots[$aggPlotKey]['paid_renta_nat_by'] = '';

            if (!$aggPlots[$aggPlotKey]['paid_renta_nat_by_arr']) {
                // This array is needed for payroll xls export
                $aggPlots[$aggPlotKey]['paid_renta_nat_by_arr'] = [
                    'amount' => 0,
                    'nat_amount' => [],
                ];
            }
            if (!empty($aggPlots[$aggPlotKey]['paid_nat_via_money'])) {
                ksort($aggPlots[$aggPlotKey]['paid_nat_via_money']);
                foreach ($aggPlots[$aggPlotKey]['paid_nat_via_money'] as $paidRentaNatViaMoney) {
                    if (0 == $paidRentaNatViaMoney['nat_trans_amount']) {
                        continue;
                    }
                    $hasPaidRenta = true;
                    $aggPlots[$aggPlotKey]['paid_renta_nat_by_detailed'] .= number_format($paidRentaNatViaMoney['nat_trans_amount'], $this->rentaNatPrecision, '.', '') . ' ' . $paidRentaNatViaMoney['nat_trans_type_unit_name'] . ' ' . $paidRentaNatViaMoney['trans_nat_type_text'] . ' Х ' . BGNtoEURO($paidRentaNatViaMoney['nat_unit_value'], $this->moneyPrecision) . '<br>';
                    $aggPlots[$aggPlotKey]['paid_renta_nat_by'] = BGNtoEURO(($paidRentaNatViaMoney['nat_trans_amount'] * $paidRentaNatViaMoney['nat_unit_value']), $this->moneyPrecision) . ' <br>';
                    $aggPlots[$aggPlotKey]['paid_renta_nat_by_arr']['amount'] += number_format(($paidRentaNatViaMoney['nat_trans_amount'] * $paidRentaNatViaMoney['nat_unit_value']), $this->moneyPrecision, '.', '');
                }
            }

            if (!empty($aggPlots[$aggPlotKey]['paid_nat_via_nat'])) {
                ksort($aggPlots[$aggPlotKey]['paid_nat_via_nat']);
                foreach ($aggPlots[$aggPlotKey]['paid_nat_via_nat'] as $paidRentaNatViaNat) {
                    if (0 == $paidRentaNatViaNat['nat_trans_amount']) {
                        continue;
                    }
                    $hasPaidRenta = true;
                    $aggPlots[$aggPlotKey]['paid_renta_nat_by_detailed'] .= number_format($paidRentaNatViaNat['nat_trans_amount'], $this->rentaNatPrecision, '.', '') . ' ' . $paidRentaNatViaNat['nat_trans_type_unit_name'] . ' ' . $paidRentaNatViaNat['trans_nat_type_text'] . '<br>';
                    $aggPlots[$aggPlotKey]['paid_renta_nat_by'] .= number_format($paidRentaNatViaNat['nat_trans_amount'], $this->rentaNatPrecision, '.', '') . ' ' . $paidRentaNatViaNat['nat_trans_type_unit_name'] . ' ' . $paidRentaNatViaNat['trans_nat_type_text'] . '<br>';
                    $aggPlots[$aggPlotKey]['paid_renta_nat_by_arr']['nat_amount'][$paidRentaNatViaNat['nat_type_id']] = number_format($paidRentaNatViaNat['nat_trans_amount'], $this->rentaNatPrecision, '.', '');
                }
            }

            if (!$hasPaidRenta) {
                $aggPlots[$aggPlotKey]['paid_renta_nat_by'] = '-';
                $aggPlots[$aggPlotKey]['paid_renta_nat_by_detailed'] = '-';
            }

            if (!empty($aggPlots[$aggPlotKey]['unpaid_renta_nat_arr'])) {
                ksort($aggPlots[$aggPlotKey]['unpaid_renta_nat_arr']);
                $aggPlots[$aggPlotKey]['unpaid_renta_nat'] = implode('<br>', array_map(function ($unpaidRentaNat) {
                    return number_format($unpaidRentaNat, $this->rentaNatPrecision, '.', '');
                }, $aggPlots[$aggPlotKey]['unpaid_renta_nat_arr']));

                $aggPlots[$aggPlotKey]['unpaid_renta_nat_with_type'] = '';
                foreach ($aggPlots[$aggPlotKey]['unpaid_renta_nat_arr'] as $rentaNatId => $unpaidRentaNat) {
                    // This array is used in payroll xls export
                    $unpaidRentaNatMoney = Math::mul($unpaidRentaNat, $aggPlots[$aggPlotKey]['renta_nat_info'][$rentaNatId]['unit_value']);
                    $aggPlots[$aggPlotKey]['unpaid_renta_nat_unit_value_arr'][$rentaNatId] = number_format($unpaidRentaNatMoney, $this->moneyPrecision, '.', '');
                    $aggPlots[$aggPlotKey]['unpaid_renta_nat_with_type'] .= number_format($unpaidRentaNat, $this->rentaNatPrecision, '.', '') . ' X ' . $aggPlots[$aggPlotKey]['renta_nat_info'][$rentaNatId]['renta_nat_name'] . '(' . $aggPlots[$aggPlotKey]['renta_nat_info'][$rentaNatId]['unit_name'] . ')<br>';
                }
            } else {
                $aggPlots[$aggPlotKey]['unpaid_renta_nat'] = '-';
                $aggPlots[$aggPlotKey]['unpaid_renta_nat_unit_value'] = '-';
                $aggPlots[$aggPlotKey]['unpaid_renta_nat_unit_value_arr'] = [];
            }

            if (!empty($aggPlots[$aggPlotKey]['overpaid_renta_nat_arr'])) {
                ksort($aggPlots[$aggPlotKey]['overpaid_renta_nat_arr']);
                $aggPlots[$aggPlotKey]['over_paid_nat'] = implode('<br>', array_map(function ($unpaidRentaNat) {
                    return number_format($unpaidRentaNat, $this->rentaNatPrecision, '.', '');
                }, $aggPlots[$aggPlotKey]['overpaid_renta_nat_arr']));

                $aggPlots[$aggPlotKey]['over_paid_nat_text'] = implode('<br>', array_map(function ($unpaidRentaNat) {
                    return number_format($unpaidRentaNat, $this->rentaNatPrecision, '.', '');
                }, $aggPlots[$aggPlotKey]['overpaid_renta_nat_arr']));

                $aggPlots[$aggPlotKey]['overpaid_renta_nat_with_type'] = '';
                foreach ($aggPlots[$aggPlotKey]['overpaid_renta_nat_arr'] as $rentaNatId => $overpaidRentaNat) {
                    $aggPlots[$aggPlotKey]['overpaid_renta_nat_with_type'] .= number_format($overpaidRentaNat, $this->rentaNatPrecision, '.', '') . ' X ' . $aggPlots[$aggPlotKey]['renta_nat_info'][$rentaNatId]['renta_nat_name'] . '(' . $aggPlots[$aggPlotKey]['renta_nat_info'][$rentaNatId]['unit_name'] . ')<br>';
                }
            } else {
                $aggPlots[$aggPlotKey]['over_paid_nat'] = '-';
                $aggPlots[$aggPlotKey]['over_paid_nat_text'] = '-';
            }

            if (!empty($aggPlots[$aggPlotKey]['unpaid_renta_nat_money_arr'])) {
                ksort($aggPlots[$aggPlotKey]['unpaid_renta_nat_money_arr']);
                $aggPlots[$aggPlotKey]['unpaid_renta_nat_unit_value'] = implode('<br>', array_map(function ($unpaidRentaNatMoney) {
                    return BGNtoEURO($unpaidRentaNatMoney, $this->moneyPrecision);
                }, $aggPlots[$aggPlotKey]['unpaid_renta_nat_money_arr']));
            }

            $aggPlots[$aggPlotKey]['unpaid_renta_nat_text'] = $aggPlots[$aggPlotKey]['unpaid_renta_nat'];
            $aggPlots[$aggPlotKey]['nat_type_ids'] = array_keys($aggPlots[$aggPlotKey]['renta_nat']);
            $aggPlots[$aggPlotKey]['renta_nat_type_id'] = $aggPlots[$aggPlotKey]['nat_type_ids'];

            // Format dates in bulgarian standart
            $contractStartDate = date('d.m.Y', strtotime($aggPlots[$aggPlotKey]['contract_start_date']));
            $contractDueDate = date('d.m.Y', strtotime($aggPlots[$aggPlotKey]['contract_due_date']));
            $aggPlots[$aggPlotKey]['timespan'] = $contractStartDate . ' - ' . $contractDueDate;

            // Check the value can be converted to date
            if (strtotime($aggPlots[$aggPlotKey]['osz_date'])) {
                $aggPlots[$aggPlotKey]['osz_date'] = date('d.m.Y', strtotime($aggPlots[$aggPlotKey]['osz_date']));
            }

            // Форматиране на личното ползване
            $aggPlots[$aggPlotKey]['personal_use_nat_types_names'] = implode('<br>', array_map(function ($renta) {
                if (empty($renta)) {
                    return '[Няма въведена натура]';
                }

                return $renta;
            }, $aggPlots[$aggPlotKey]['personal_use_nat_types_names_arr']));

            if (empty($aggPlots[$aggPlotKey]['personal_use_nat_types_names'])) {
                $aggPlots[$aggPlotKey]['personal_use_nat_types_names'] = '-';
            }

            $aggPlots[$aggPlotKey]['personal_use_renta'] = implode('<br>', array_map(function ($renta) {
                return number_format($renta, $this->moneyPrecision, '.', '');
            }, $aggPlots[$aggPlotKey]['personal_use_renta_arr']));

            if (empty($aggPlots[$aggPlotKey]['personal_use_renta'])) {
                $aggPlots[$aggPlotKey]['personal_use_renta'] = '-';
            }

            $aggPlots[$aggPlotKey]['personal_use_paid_renta'] = implode('<br>', array_map(function ($renta) {
                return number_format($renta, $this->moneyPrecision, '.', '');
            }, $aggPlots[$aggPlotKey]['personal_use_paid_renta_arr']));

            if (empty($aggPlots[$aggPlotKey]['personal_use_paid_renta'])) {
                $aggPlots[$aggPlotKey]['personal_use_paid_renta'] = '-';
            }

            $aggPlots[$aggPlotKey]['personal_use_unpaid_renta'] = implode('<br>', array_map(function ($renta) {
                return number_format($renta, $this->moneyPrecision, '.', '');
            }, $aggPlots[$aggPlotKey]['personal_use_unpaid_renta_arr']));

            if (empty($aggPlots[$aggPlotKey]['personal_use_unpaid_renta'])) {
                $aggPlots[$aggPlotKey]['personal_use_unpaid_renta'] = '-';
            }

            $aggPlots[$aggPlotKey]['personal_use_treatments_sum'] = implode('<br>', array_map(function ($renta) {
                return BGNtoEURO($renta, $this->moneyPrecision);
            }, $aggPlots[$aggPlotKey]['personal_use_treatments_sum_arr']));

            if (empty($aggPlots[$aggPlotKey]['personal_use_treatments_sum'])) {
                $aggPlots[$aggPlotKey]['personal_use_treatments_sum'] = '-';
            }

            $aggPlots[$aggPlotKey]['personal_use_paid_treatments'] = implode('<br>', array_map(function ($renta) {
                return BGNtoEURO($renta, $this->moneyPrecision);
            }, $aggPlots[$aggPlotKey]['personal_use_paid_treatments_arr']));

            if (empty($aggPlots[$aggPlotKey]['personal_use_paid_treatments'])) {
                $aggPlots[$aggPlotKey]['personal_use_paid_treatments'] = '-';
            }

            $aggPlots[$aggPlotKey]['personal_use_unpaid_treatments'] = implode('<br>', array_map(function ($renta) {
                return BGNtoEURO($renta, $this->moneyPrecision);
            }, $aggPlots[$aggPlotKey]['personal_use_unpaid_treatments_arr']));

            if (empty($aggPlots[$aggPlotKey]['personal_use_unpaid_treatments'])) {
                $aggPlots[$aggPlotKey]['personal_use_unpaid_treatments'] = '-';
            }

            $aggPlots[$aggPlotKey]['all_owner_no_rounded'] = (string)$aggPlots[$aggPlotKey]['all_owner_no_rounded'];
            $aggPlots[$aggPlotKey]['all_owner_no_rounded_contract'] = (string)$aggPlots[$aggPlotKey]['all_owner_no_rounded_contract'];
            $aggPlots[$aggPlotKey]['over_paid'] = number_format($aggPlots[$aggPlotKey]['overpaid_renta'], $this->moneyPrecision, '.', '');
            $aggPlots[$aggPlotKey]['all_owner_area'] = number_format($aggPlots[$aggPlotKey]['all_owner_area'], $this->areaPrecision, '.', '');
            $aggPlots[$aggPlotKey]['all_owner_contract_area'] = number_format($aggPlots[$aggPlotKey]['all_owner_contract_area'], $this->areaPrecision, '.', '');
            $aggPlots[$aggPlotKey]['owner_area'] = number_format($aggPlots[$aggPlotKey]['owner_area'], $this->areaPrecision, '.', '');
            $aggPlots[$aggPlotKey]['cultivated_area'] = number_format($aggPlots[$aggPlotKey]['cultivated_area'], $this->areaPrecision, '.', '');
            $aggPlots[$aggPlotKey]['pu_area'] = is_numeric($aggPlots[$aggPlotKey]['pu_area']) ? number_format($aggPlots[$aggPlotKey]['pu_area'], $this->areaPrecision, '.', '') : $aggPlots[$aggPlotKey]['pu_area'];
            $aggPlots[$aggPlotKey]['paid_renta'] = number_format($aggPlots[$aggPlotKey]['paid_renta'], $this->moneyPrecision, '.', '');
            $aggPlots[$aggPlotKey]['renta'] = number_format($aggPlots[$aggPlotKey]['renta'], $this->moneyPrecision, '.', '');
            $aggPlots[$aggPlotKey]['charged_renta'] = is_numeric($aggPlots[$aggPlotKey]['charged_renta']) ? number_format($aggPlots[$aggPlotKey]['charged_renta'], $this->moneyPrecision, '.', '') : $aggPlots[$aggPlotKey]['charged_renta'];
            $aggPlots[$aggPlotKey]['charged_renta_value'] = number_format($aggPlots[$aggPlotKey]['charged_renta_value'], $this->moneyPrecision, '.', '');
            $aggPlots[$aggPlotKey]['unpaid_renta'] = number_format($aggPlots[$aggPlotKey]['unpaid_renta'], $this->moneyPrecision, '.', '');
            $aggPlots[$aggPlotKey]['all_renta_money'] = number_format($aggPlots[$aggPlotKey]['renta'] + $aggPlots[$aggPlotKey]['charged_renta'], $this->moneyPrecision, '.', '');

            $aggPlots[$aggPlotKey]['contract_renta_value'] = number_format($aggPlots[$aggPlotKey]['contract_renta_value'], $this->moneyPrecision, '.', '');
            $aggPlots[$aggPlotKey]['contract_renta'] = number_format($aggPlots[$aggPlotKey]['contract_renta'], $this->moneyPrecision, '.', '');

            $aggPlots[$aggPlotKey]['renta_txt'] = BGNtoEURO($aggPlots[$aggPlotKey]['renta'], $this->moneyPrecision);
            if (is_numeric($aggPlots[$aggPlotKey]['charged_renta'])) {
                $aggPlots[$aggPlotKey]['charged_renta_txt'] = BGNtoEURO($aggPlots[$aggPlotKey]['charged_renta'], $this->moneyPrecision);
            }
            $aggPlots[$aggPlotKey]['unpaid_renta_txt'] = BGNtoEURO($aggPlots[$aggPlotKey]['unpaid_renta'], $this->moneyPrecision);
            $aggPlots[$aggPlotKey]['paid_renta_txt'] = BGNtoEURO($aggPlots[$aggPlotKey]['paid_renta'], $this->moneyPrecision);
            $aggPlots[$aggPlotKey]['contract_renta_txt'] = BGNtoEURO($aggPlots[$aggPlotKey]['contract_renta'], $this->moneyPrecision);
            $aggPlots[$aggPlotKey]['over_paid_txt'] = BGNtoEURO($aggPlots[$aggPlotKey]['over_paid'], $this->moneyPrecision);

            // Recursively process nested arrays
            if (!empty($aggPlots[$aggPlotKey]['children'])) {
                $aggPlots[$aggPlotKey]['children'] = $this->formattingOwnersData($aggPlots[$aggPlotKey]['children']);
            }
        }

        // Ключовете се ресестват заради datagrid-a на easyUI-a
        return array_values($aggPlots);
    }

    public function getPersonalUseForOwners($params, $counter = false, $returnOnlySQL = false)
    {
        // build main query(getting all personal use payments)
        $options = [
            'return' => [
                'pu.owner_id as owner_id',
                'c.id as contract_id',
                'c.c_num as c_num',
                'spur.renta_type',
                'spur.unit_value as personal_use_unit_value',
                'srt."name" as renta_type_name',
                'sum(sum(round(spur.area::numeric, 3))) over (PARTITION BY owner_id, c.id) as total_personal_use_area',
                'json_agg(json_build_object(
                    \'pc_rel_id\', pu.pc_rel_id,
                    \'area\', round(spur.area::numeric, 3)
                )) as personal_use_plots_area',
                'sum(round(spur.area::numeric, 3)) as area_by_renta_type',
                'sum(round(spur.area::numeric, 3) * spur.renta_per_dka) as personal_use_renta',
                '(
                    select
                        sum(scpr.area_for_rent * spor.percent / 100)	
                    from su_contracts_plots_rel scpr
                    inner join su_plots_owners_rel spor on spor.pc_rel_id = scpr.id
                    where 
                        spor.owner_id = pu.owner_id
                        and scpr.contract_id = coalesce(a.id, c.id)
                ) as total_owned_area',
                'sum(round(spur.area::numeric, 3) * coalesce(spur.treatments_price, 0)) as personal_use_treatments_sum',
                '(
                    SELECT COALESCE(sum(amount), 0)
                    FROM public.su_collections
                    where
                        contract_id = coalesce(a.id, c.id)
                        and "type" = ' . TRANSACTION_TYPE_PERSONAL_USE . '
                        AND status = true
                        AND farming_year IN (' . $params['chosen_years'] . ')
                        AND payment_data->>\'owner_id\' = pu.owner_id::text 
                        AND payment_data->>\'renta_type_id\' = srt.id::text
                    ) as personal_use_paid_treatments',
                'sum(round(spur.area::numeric, 3) * coalesce(spur.treatments_price, 0)) - (
                    SELECT COALESCE(sum(amount), 0)
                    FROM public.su_collections
                    where
                        contract_id = coalesce(a.id, c.id)
                        and "type" = ' . TRANSACTION_TYPE_PERSONAL_USE . '
                        AND status = true
                        AND farming_year IN (' . $params['chosen_years'] . ')
                        AND payment_data->>\'owner_id\' = pu.owner_id::text 
                        AND payment_data->>\'renta_type_id\' = srt.id::text
                ) as personal_use_unpaid_treatments',
                '(
                    select 
                        sum(stn.amount)
                    from su_transactions st
                    left join su_transactions_natura stn on stn.transaction_id = st.id 
                    left join su_payments sp on sp.transaction_id = st.id
                    where 
                        st.type = ' . TRANSACTION_TYPE_PERSONAL_USE . '
                        and st.status = true
                        and sp.owner_id = pu.owner_id
                        and sp.farming_year IN (' . $params['chosen_years'] . ')
                        and sp.contract_id = coalesce(a.id, c.id)
                        and stn.nat_type = srt.id
                 ) as personal_use_paid_renta',
                'sum(round(spur.area::numeric, 3) * spur.renta_per_dka) - 
                 coalesce ((
                    select 
                        sum(stn.amount)
                    from su_transactions st
                    left join su_transactions_natura stn on stn.transaction_id = st.id 
                    left join su_payments sp on sp.transaction_id = st.id
                    where 
                        st.type = ' . TRANSACTION_TYPE_PERSONAL_USE . '
                        and st.status = true
                        and sp.owner_id = pu.owner_id
                        and sp.farming_year IN (' . $params['chosen_years'] . ')
                        and sp.contract_id = coalesce(a.id, c.id)
                        and stn.nat_type = srt.id
                 ), 0) as personal_use_unpaid_renta',
            ],
            'where' => [
                'annex_action' => ['column' => 'annex_action', 'compare' => '=', 'prefix' => 'pc', 'value' => 'added'],
            ],
            'group' => 'pu.owner_id, c.id, a.id, spur.renta_type, srt."name", srt.id, spur.unit_value',
            'custom_counter' => 'COUNT(DISTINCT(o.id))',
            'chosen_years' => $params['chosen_years'],
        ];
        if ($params['owner_ids']) {
            $options['where']['owner_id'] = ['column' => 'owner_id', 'compare' => 'IN', 'prefix' => 'pu', 'value' => $params['owner_ids']];
        }
        if ($params['contract_id']) {
            $options['contract_id_string'] = $params['contract_id'];
        }
        if ($params['year'] || $params['chosen_years']) {
            $options['start_date'] = $GLOBALS['Farming']['years'][$params['year'] ?? $params['chosen_years']]['start_date'];
            $options['due_date'] = $GLOBALS['Farming']['years'][$params['year'] ?? $params['chosen_years']]['end_date'];
        }

        return $this->DbHandler->getPersonalUseForOwners($options, $counter, $returnOnlySQL);
    }

    public function getPersonalUseArea($personalUse, $ownerID, $plot)
    {
        $puArea = 0;
        foreach ($personalUse as $ownerPersonalUse) {
            if ($ownerPersonalUse['owner_id'] == $ownerID && $ownerPersonalUse['contract_id'] == $plot['contract_id']) {
                $ownerPersonalUsePlots = json_decode($ownerPersonalUse['personal_use_plots_area'], true);
                // Проверяваме дали дадения имот има лично ползване
                foreach ($ownerPersonalUsePlots as $ownerPersonalUsePlot) {
                    if ($ownerPersonalUsePlot['pc_rel_id'] == $plot['pc_rel_id']) {
                        $partCoef = Math::div($plot['plot_owned_area'], $plot['plot_owned_area_total']);
                        // Намираме, каква част от личното ползване е за конкретния собственик т.к. може да се явява и като наследник на друг собственик
                        $tempPuArea = Math::mul($ownerPersonalUsePlot['area'], $partCoef);
                        // Намираме площта на личното ползване за имота
                        $puArea = Math::add($puArea, $tempPuArea);
                    }
                }
            }
        }

        return $puArea;
    }

    public function getContractOwnerPayments($year, $contractAnnexIds = null, $ownerIdPath = null)
    {
        $UserDbPaymentsController = new UserDbPaymentsController($this->Database);
        // Вземат се плащанията за собствениците критерии
        $paidOptions = [
            'custom_counter' => 'COUNT(DISTINCT(p.owner_id))',
            'order' => 'asc',
            'sort' => 'p.contract_id',
            'return' => [
                'p.id as payment_id',
                'p.owner_id as owner_id',
                'p.path as owner_path',
                'p.owner_id || \'_\' || COALESCE(p.path::text, 0::text) as owner_path_key',
                'p.contract_id as contract_id',
                'case when pn.amount notnull and pn.unit_value notnull  then pn.amount::numeric * pn.unit_value else p.amount::numeric end as trans_amount',
                'pn.amount::numeric as trans_amount_nat',
                'case when pn.amount notnull then pn.amount::numeric else p.amount_nat::numeric end as amount_nat',
                'pn.unit_value::numeric as unit_value',
                'pn.nat_type as nat_type',
                'p.paid_in',
                'p.paid_from',
                'rent.name as trans_nat_type_text',
                'rent.unit as trans_nat_type_unit',
                'p.farming_year as year',
            ],
            'where' => [
                'contract_id' => ['column' => 'contract_id', 'compare' => 'IN', 'prefix' => 'p', 'value' => $contractAnnexIds],
                'owner_id' => ['column' => 'owner_id', 'compare' => '=', 'prefix' => 'p', 'value' => $ownerIdPath],
                'farming_year' => ['column' => 'farming_year', 'compare' => '=', 'prefix' => 'p', 'value' => $year],
            ],
        ];

        if (is_string($ownerIdPath)) {
            $paidOptions['where']['owner_path'] = ['column' => 'path', 'compare' => '=', 'prefix' => 'p', 'value' => $ownerIdPath];
            unset($paidOptions['where']['owner_id']);
        }

        return $UserDbPaymentsController->getPaidData($paidOptions, false, false);
    }

    public function calculateOwnerData($owner, $secondOwner)
    {
        $ownerInit = $this->calculateIDandPathRentaNatura($owner, $secondOwner);

        $contractWithGroupName = !empty($secondOwner['contract_group_name']) ? $secondOwner['c_num'] . ' (' . $secondOwner['contract_group_name'] . ')' : $secondOwner['c_num'];

        if ($ownerInit['init']) {
            $ownerInit['owner']['c_num_group_name_array'] = array_unique([...($ownerInit['owner']['c_num_array'] ?? []), $contractWithGroupName]);
            $ownerInit['owner']['c_num_array'] = array_unique([...($ownerInit['owner']['c_num_array'] ?? []), $secondOwner['c_num']]);
            $ownerInit['owner']['kad_idents_array'] = array_unique([...($ownerInit['owner']['kad_idents_array'] ?? []), ...($secondOwner['kad_idents_array'] ?? [])]);
            $ownerInit['owner']['rep_names_array'] = array_unique([...($ownerInit['owner']['rep_names_array'] ?? []), ...($secondOwner['rep_names_array'] ?? [])]);
            $ownerInit['owner']['rep_ibans_array'] = array_unique([...($ownerInit['owner']['rep_ibans_array'] ?? []), ...($secondOwner['rep_ibans_array'] ?? [])]);

            return $ownerInit['owner'];
        }

        $owner = $ownerInit['owner'];
        $owner['c_num_group_name_array'] = array_unique([...($owner['c_num_group_name_array'] ?? []), $contractWithGroupName]);
        $owner['c_num_array'] = array_unique([...($owner['c_num_array'] ?? []), $secondOwner['c_num']]);
        $owner['kad_idents_array'] = array_unique([...($owner['kad_idents_array'] ?? []), ...($secondOwner['kad_idents_array'] ?? [])]);
        $owner['rep_names_array'] = array_unique([...($owner['rep_names_array'] ?? []), ...($secondOwner['rep_names_array'] ?? [])]);
        $owner['rep_ibans_array'] = array_unique([...($owner['rep_ibans_array'] ?? []), ...($secondOwner['rep_ibans_array'] ?? [])]);
        $owner['all_owner_area'] = Math::add($owner['all_owner_area'], $secondOwner['all_owner_area'], $this->areaPrecision);
        $owner['all_owner_contract_area'] = Math::add($owner['all_owner_contract_area'], $secondOwner['all_owner_contract_area'], $this->areaPrecision);
        $owner['pu_area'] = Math::add($owner['pu_area'], $secondOwner['pu_area'], $this->areaPrecision);
        $owner['owner_area'] = Math::add($owner['owner_area'], $secondOwner['owner_area'], $this->areaPrecision);
        $owner['cultivated_area'] = Math::add($owner['cultivated_area'], $secondOwner['cultivated_area'], $this->areaPrecision);
        $owner['all_owner_no_rounded'] = Math::add($owner['all_owner_no_rounded'], $secondOwner['all_owner_no_rounded']);
        $owner['all_owner_no_rounded_contract'] = Math::add($owner['all_owner_no_rounded_contract'], $secondOwner['all_owner_no_rounded_contract']);
        $owner['renta'] = Math::add($owner['renta'], $secondOwner['renta'], $this->moneyPrecision);
        $owner['paid_renta'] = Math::add($owner['paid_renta'], $secondOwner['paid_renta'], $this->moneyPrecision);
        $owner['paid_via_money'] = Math::add($owner['paid_via_money'], $secondOwner['paid_via_money'], $this->moneyPrecision);
        $owner['unpaid_renta'] = Math::add($owner['unpaid_renta'], $secondOwner['unpaid_renta'], $this->moneyPrecision);
        $owner['overpaid_renta'] = Math::add($owner['overpaid_renta'], $secondOwner['overpaid_renta'], $this->moneyPrecision);
        $owner['over_paid'] = Math::add($owner['over_paid'], $secondOwner['over_paid'], $this->moneyPrecision);
        $owner['contract_renta'] = Math::add($owner['contract_renta'], $secondOwner['contract_renta'], $this->moneyPrecision);

        if (is_numeric(($owner['charged_renta']))) {
            $owner['charged_renta'] = Math::add($owner['charged_renta'], $secondOwner['charged_renta'], $this->moneyPrecision);
        }

        if (!is_numeric(($owner['charged_renta'])) && is_numeric(($secondOwner['charged_renta']))) {
            $owner['charged_renta'] = Math::add(0, $secondOwner['charged_renta'], $this->moneyPrecision);
        }

        foreach ($secondOwner['renta_nat'] as $rentaNatId => $rentaNatValue) {
            if (!isset($owner['renta_nat'][$rentaNatId])) {
                $owner['renta_nat'][$rentaNatId] = 0;
                $owner['renta_nat_names'][$rentaNatId] = 0;
                // $owner['charged_renta_nat'][$rentaNatId] = 0;
                $owner['renta_nat_info'][$rentaNatId] = $secondOwner['renta_nat_info'][$rentaNatId];
            }

            $owner['renta_nat'][$rentaNatId] = Math::add($owner['renta_nat'][$rentaNatId], $rentaNatValue);
            $owner['renta_nat_names'][$rentaNatId] = Math::add($owner['renta_nat_names'][$rentaNatId], $rentaNatValue);
        }

        foreach ($secondOwner['charged_renta_nat'] as $chargedRentaNatId => $chargedRentaNatValue) {
            if (!is_numeric($chargedRentaNatValue)) {
                continue;
            }
            if (!isset($owner['charged_renta_nat'][$chargedRentaNatId]) || !is_numeric($owner['charged_renta_nat'][$chargedRentaNatId])) {
                $owner['charged_renta_nat'][$chargedRentaNatId] = 0;
            }

            $owner['charged_renta_nat'][$chargedRentaNatId] = Math::add($owner['charged_renta_nat'][$chargedRentaNatId], $chargedRentaNatValue);
        }

        foreach ($secondOwner['unpaid_renta_nat_arr'] as $unpaidRentaNat => $unpaidRentaNatValue) {
            if (!isset($owner['unpaid_renta_nat_arr'][$unpaidRentaNat])) {
                $owner['unpaid_renta_nat_arr'][$unpaidRentaNat] = 0;
            }
            $owner['unpaid_renta_nat_arr'][$unpaidRentaNat] = Math::add($owner['unpaid_renta_nat_arr'][$unpaidRentaNat], $unpaidRentaNatValue);
        }

        foreach ($secondOwner['overpaid_renta_nat_arr'] as $overpaidRentaNat => $overpaidRentaNatValue) {
            if (!isset($owner['overpaid_renta_nat_arr'][$overpaidRentaNat])) {
                $owner['overpaid_renta_nat_arr'][$overpaidRentaNat] = 0;
            }
            $owner['overpaid_renta_nat_arr'][$overpaidRentaNat] = Math::add($owner['overpaid_renta_nat_arr'][$overpaidRentaNat], $overpaidRentaNatValue);
        }

        foreach ($secondOwner['paid_nat_via_money'] as $paidNatViaNatKey => $paidNatViaNatValue) {
            if (!isset($owner['paid_nat_via_money'][$paidNatViaNatKey])) {
                $owner['paid_nat_via_money'][$paidNatViaNatKey] = $paidNatViaNatValue;
            } else {
                $owner['paid_nat_via_money'][$paidNatViaNatKey]['nat_trans_amount'] = Math::add($owner['paid_nat_via_money'][$paidNatViaNatKey]['nat_trans_amount'], $paidNatViaNatValue['nat_trans_amount']);
            }
        }

        foreach ($secondOwner['paid_nat_via_nat'] as $paidNatViaNatKey => $paidNatViaNatValue) {
            if (!isset($owner['paid_nat_via_nat'][$paidNatViaNatKey])) {
                $owner['paid_nat_via_nat'][$paidNatViaNatKey] = $paidNatViaNatValue;
            } else {
                $owner['paid_nat_via_nat'][$paidNatViaNatKey]['nat_trans_amount'] = Math::add($owner['paid_nat_via_nat'][$paidNatViaNatKey]['nat_trans_amount'], $paidNatViaNatValue['nat_trans_amount']);
            }
        }

        foreach ($secondOwner['paid_renta_nat_sum'] as $paidRentaNatSumKey => $paidRentaNatSumValue) {
            if (!isset($owner['paid_renta_nat_sum'][$paidRentaNatSumKey])) {
                $owner['paid_renta_nat_sum'][$paidRentaNatSumKey] = 0;
            }
            $owner['paid_renta_nat_sum'][$paidRentaNatSumKey] = Math::add($owner['paid_renta_nat_sum'][$paidRentaNatSumKey], $paidRentaNatSumValue);
        }

        foreach ($secondOwner['unpaid_renta_nat_money_arr'] as $unpaidRentaNatMoney => $unpaidRentaNatMoneyValue) {
            if (!isset($owner['unpaid_renta_nat_money_arr'][$unpaidRentaNatMoney])) {
                $owner['unpaid_renta_nat_money_arr'][$unpaidRentaNatMoney] = 0;
            }
            $owner['unpaid_renta_nat_money_arr'][$unpaidRentaNatMoney] = Math::add($owner['unpaid_renta_nat_money_arr'][$unpaidRentaNatMoney], $unpaidRentaNatMoneyValue);
        }

        foreach ($secondOwner['paid_via_nat'] as $paidViaNatKey => $paidViaNatValue) {
            if (!isset($owner['paid_via_nat'][$paidViaNatKey])) {
                $owner['paid_via_nat'][$paidViaNatKey] = $paidViaNatValue;

                continue;
            }
            $owner['paid_via_nat'][$paidViaNatKey]['nat_trans_amount'] = Math::add($owner['paid_via_nat'][$paidViaNatKey]['nat_trans_amount'], $paidViaNatValue['nat_trans_amount']);
        }

        return $owner;
    }

    private function flattenOwnersTree($tree)
    {
        $result = [];

        foreach ($tree as $owner) {
            if (isset($owner['children'])) {
                $childResult = $this->flattenOwnersTree($owner['children']);
                $result += $childResult;

                // Remove children key after processing
                unset($owner['children']);
            }

            $key = $owner['path'] ?? $owner['owner_id'];

            // Add the current node to the result if it matches or has a matching child
            $result[$key] = $owner;
        }

        return $result;
    }

    private function filterOwnersTree(array $ownersTree, $filterParams = [])
    {
        $result = [];

        foreach ($ownersTree as $key => $owner) {
            $ownerMatches = false;
            $hasMatchingChildren = false;
            $filteredChildren = [];

            // Check if current owner matches the filter criteria
            if (!empty($filterParams['owner_names'])) {
                // For owner_names filter, only match actual owners (level 1), not heritors
                $tmp_person_names = mb_strtolower(trim($filterParams['owner_names']), 'UTF-8');

                // For owner_names filter, only match actual owners (level 1), not heritors
                if (isset($owner['level']) && 1 == $owner['level']
                    && false !== mb_strpos(mb_strtolower($owner['owner_names'], 'UTF-8'), $tmp_person_names)) {
                    $ownerMatches = true;
                }
            } elseif (!empty($filterParams['egn'])) {
                // For egn filter, only match actual owners (level 1), not heritors
                if (isset($owner['level']) && 1 == $owner['level']
                    && $owner['egn_eik'] == $filterParams['egn']) {
                    $ownerMatches = true;
                }
            } elseif (!empty($filterParams['heritor_names'])) {
                $tmp_person_names = mb_strtolower(trim($filterParams['heritor_names']), 'UTF-8');

                // For heritor_names filter, match heritors (level > 1)
                if (isset($owner['level']) && $owner['level'] > 1
                    && false !== mb_strpos(mb_strtolower($owner['owner_names'], 'UTF-8'), $tmp_person_names)) {
                    $ownerMatches = true;
                }
            } elseif (!empty($filterParams['heritor_egn'])) {
                // For heritor filters, we need to check if this owner is a heritor that matches
                if (isset($owner['level']) && $owner['level'] > 1
                    && $owner['egn_eik'] == $filterParams['heritor_egn']) {
                    $ownerMatches = true;
                }
            } else {
                // No filter specified, include all
                $ownerMatches = true;
            }

            // Recursively filter children if they exist
            if (isset($owner['children']) && !empty($owner['children'])) {
                $filteredChildren = $this->filterOwnersTree($owner['children'], $filterParams);
                $hasMatchingChildren = !empty($filteredChildren);
            }

            $ownerIdsMatch = in_array($owner['owner_id'], $filterParams['owner_ids']);

            // Include this owner if:
            // 1. The owner itself matches the filter, OR
            // 2. The owner has matching children (to preserve tree structure)
            if (($ownerMatches && $ownerIdsMatch) || $hasMatchingChildren) {
                $result[$key] = $owner;

                // If we have filtered children, replace the children array
                if ($hasMatchingChildren) {
                    $result[$key]['children'] = $filteredChildren;
                }
            }
        }

        return $result;
    }

    /**
     * Filter children based on parent's dead status and farm year logic
     * Business Rules:
     * 1. If parent is_dead = false → DO NOT add children (hide children)
     * 2. If parent is_dead = true AND dead_date_in_current_farm_year = false → EXCLUDE children (hide children)
     * 3. If parent is_dead = true AND dead_date_in_current_farm_year = true → ADD children (show children).
     */
    private function filterChildrenByParentDeadStatus(array $aggPlots): array
    {
        $filteredPlots = [];

        // Create a lookup map of all owners by their ID for quick access
        $ownerLookup = [];
        foreach ($aggPlots as $plotKey => $plot) {
            $ownerLookup[$plot['owner_id']] = $plot;
        }

        // Filter all plots based on hierarchical business rules
        foreach ($aggPlots as $plotKey => $plot) {
            $shouldInclude = true;

            if (empty($plot['path'])) {
                // This is a root parent - always include
                $shouldInclude = true;
            } else {
                // This is a child - check the entire parent hierarchy
                $shouldInclude = $this->shouldIncludeChildInHierarchy($plot, $ownerLookup);
            }

            // Include or exclude the plot
            if ($shouldInclude) {
                $filteredPlots[$plotKey] = $plot;
            }
        }

        return $filteredPlots;
    }

    /**
     * Check if a child should be included by examining the entire parent hierarchy
     * Each level of parent must allow children to be shown.
     */
    private function shouldIncludeChildInHierarchy(array $child, array $ownerLookup): bool
    {
        $pathParts = explode('.', $child['path']);

        // Check each level of the hierarchy from root to immediate parent
        for ($i = 0; $i < count($pathParts) - 1; $i++) {
            $parentId = $pathParts[$i];

            if (isset($ownerLookup[$parentId])) {
                $parent = $ownerLookup[$parentId];

                // If any parent in the hierarchy should hide children, exclude this child
                if ($this->shouldHideChildren($parent)) {
                    return false;
                }
            } else {
                // Parent not found - this shouldn't happen in normal cases
                // but we'll be conservative and exclude the child
                return false;
            }
        }

        // All parents in the hierarchy allow children to be shown
        return true;
    }

    /**
     * Determine if children should be hidden based on parent's dead status.
     */
    private function shouldHideChildren(array $parent): bool
    {
        // Business Rules:
        // 1. If parent is_dead = false → hide children (alive parents don't show heritors)
        // 2. If parent is_dead = true AND dont_show_heritoris = true → hide children
        // 3. If parent is_dead = true AND dont_show_heritoris = false → show children

        if (!$parent['is_dead']) {
            return true; // Hide children - parent is alive
        }

        // Use the dont_show_heritoris flag to determine if dead parent's children should be hidden
        return (bool) $parent['dont_show_heritoris'];
    }

    private function findAllOwnersById(&$owners, $ownerIds)
    {
        $results = [];

        foreach ($owners as $owner) {
            if (in_array($owner['owner_id'], $ownerIds)) {
                $results[] = $owner;
            }
            if (!empty($owner['children'])) {
                $childResults = $this->findAllOwnersById($owner['children'], $ownerIds);
                foreach ($childResults as $childResult) {
                    // Връщаме клона, включително пътя до намерения резултат
                    $ownerCopy = $owner;
                    $ownerCopy['children'] = [$childResult];
                    $results[] = $ownerCopy;
                }
            }
        }

        return $results;
    }

    /**
     * Функцията връща сборувана информацията за собственик в договор, ако той участва повече от един път в договора (като собственик и наследник).
     * Например Собственик А притежава 50% (5 дка) от имот в Договор А и е наследник на Собственик Б, който притежава 50% (5 дка) от същия имот.
     * Функцията ще сборува данните за Собственик А и ще върне един запис с площ 10 дка
     * Използва се в Ренти->Собственици, където искаме да площите и рентите за собственика за даден договор.
     *
     * @param int $ownerId
     * @param array $owner
     *
     * @return array
     */
    private function sumOwnerDataInContract(array $contracts, $ownerId, $owner = null)
    {
        foreach ($contracts as $contract) {
            // В Ренти по собственик се иска да се сборуват рентите за собственика, който участва в договора като собственик и наследник.
            // Тогава подаваме owner_id на собственика
            if ($contract['owner_id'] === $ownerId) {
                $owner = $this->calculateOwnerData($owner, $contract);
            }

            if ($contract['children']) {
                $owner = $this->sumOwnerDataInContract($contract['children'], $ownerId, $owner);
            }
        }

        return $owner;
    }

    private function getOwnerDataFromContract(array $contractData, $ownerIdPath, $owner = null)
    {
        foreach ($contractData as $contract) {
            // Може да искаме да вземем информацията само за наследник, който може да участва и като собственик. Например във ведомостта по имот.
            // Тогава подаваме path на наследника
            if (is_int($ownerIdPath)) {
                $condition = $contract['owner_id'] === $ownerIdPath && null === $contract['path'];
            } else {
                $condition = $contract['path'] === $ownerIdPath;
            }

            if ($condition) {
                return $owner ?? $contract;
            }

            if ($contract['children']) {
                $owner = $this->getOwnerDataFromContract($contract['children'], $ownerIdPath, $owner);
            }
        }

        return $owner;
    }

    private function calculateIDandPathRentaNatura($owner, $secondOwner)
    {
        if (null === $owner) {
            $owner = $secondOwner;

            // Данните в тези масиви се използват при плащанията. Ще бъдат премахнати след рафакторинга на плащанията.
            $owner['id_renta_natura'] = [];
            $owner['path_renta_natura'] = [];
            if (!isset($owner['id_renta_natura'][$owner['owner_id']]) && empty($owner['path'])) {
                $owner['id_renta_natura'][$owner['owner_id']] = [
                    'owner_id' => $owner['owner_id'],
                    'uuid' => $owner['uuid'],
                    'contract_id' => $owner['contract_id'],
                    'farming_id' => $owner['farming_id'],
                    'area' => $owner['owner_area'],
                    'charged_renta' => $owner['charged_renta'],
                    'charged_renta_nat' => $owner['charged_renta_nat'],
                    'renta' => $owner['renta'],
                    'renta_nat' => $owner['renta_nat'],
                    'unpaid_renta' => $owner['unpaid_renta'],
                    'unpaid_renta_nat' => $owner['unpaid_renta_nat_arr'],
                ];
            }

            if (!empty($owner['path'])) {
                $owner['path_renta_natura'][$owner['path']] = [
                    'owner_id' => $owner['owner_id'],
                    'uuid' => $owner['uuid'],
                    'contract_id' => $owner['contract_id'],
                    'farming_id' => $owner['farming_id'],
                    'area' => $owner['owner_area'],
                    'charged_renta' => $owner['charged_renta'],
                    'charged_renta_nat' => $owner['charged_renta_nat'],
                    'renta' => $owner['renta'],
                    'renta_nat' => $owner['renta_nat'],
                    'unpaid_renta' => $owner['unpaid_renta'],
                    'unpaid_renta_nat' => $owner['unpaid_renta_nat_arr'],
                ];
            }

            return [
                'init' => true,
                'owner' => $owner,
            ];
        }

        if (!isset($owner['id_renta_natura'][$secondOwner['owner_id']]) && empty($secondOwner['path'])) {
            $owner['id_renta_natura'][$secondOwner['owner_id']] = [
                'owner_id' => $secondOwner['owner_id'],
                'uuid' => $secondOwner['uuid'],
                'contract_id' => $secondOwner['contract_id'],
                'farming_id' => $secondOwner['farming_id'],
                'area' => $secondOwner['owner_area'],
                'charged_renta' => $secondOwner['charged_renta'],
                'charged_renta_nat' => $secondOwner['charged_renta_nat'],
                'renta' => $secondOwner['renta'],
                'renta_nat' => $secondOwner['renta_nat'],
                'unpaid_renta' => $secondOwner['unpaid_renta'],
                'unpaid_renta_nat' => $secondOwner['unpaid_renta_nat_arr'],
            ];
        }

        if (!empty($secondOwner['path'])) {
            $owner['path_renta_natura'][$secondOwner['path']] = [
                'owner_id' => $secondOwner['owner_id'],
                'uuid' => $secondOwner['uuid'],
                'contract_id' => $secondOwner['contract_id'],
                'farming_id' => $secondOwner['farming_id'],
                'area' => $secondOwner['owner_area'],
                'charged_renta' => $secondOwner['charged_renta'],
                'charged_renta_nat' => $secondOwner['charged_renta_nat'],
                'renta' => $secondOwner['renta'],
                'renta_nat' => $secondOwner['renta_nat'],
                'unpaid_renta' => $secondOwner['unpaid_renta'],
                'unpaid_renta_nat' => $secondOwner['unpaid_renta_nat_arr'],
            ];
        }

        return [
            'init' => false,
            'owner' => $owner,
        ];
    }

    private function calculateTreeRents(array &$ownersTree)
    {
        // От дължимите ренти на наследницте се изваждат платените ренти на собствениците. Това е възможно, когато собственика е починал в текущата стопанска година
        $this->calculateChildrenRentsBasedOfParentPayments($ownersTree);

        // От дължимите ренти на собствениците се изваждат платените ренти на наследниците.
        $this->calculateParentRentsBasedOfChildrenPayments($ownersTree);
    }

    private function calculateChildrenRentsBasedOfParentPayments(array &$children, $parent = null)
    {
        foreach ($children as &$child) {
            // Тук ще се съхранява вече платената рента към родителя. Тази рента трябва да се извади от дължимата рента на наследника.
            $child['parent_paid_renta_via_money'] = 0;
            $child['parent_paid_renta_via_nat'] = 0;
            $child['parent_paid_renta_nat_sum'] = [];

            // Намиране на съотноешението между рентата на наследника и рентата на собственика. Не може да се използва процента на наследника,
            // защото той може да има лично ползване и тогава неговата рента е различна от рентата на собственика.
            if ($parent['charged_renta']) {
                $paidViaMoneyPart = Math::div($child['charged_renta'], $parent['charged_renta']);
            } else {
                $paidViaMoneyPart = Math::div($child['renta'], $parent['renta']);
            }

            // Плащане на пари в пари
            if (null !== $parent['paid_via_money']) {
                // Намиране каква част от платената рента към родителя, ще прибавим към платената рента на наследника
                $paidHeritorPart = Math::mul($paidViaMoneyPart, $parent['paid_via_money']);

                // Намиране каква част от платената рента към родителите от по-горни нива, ще прибавим към платената рента на наследника
                $parentPaidRentaPart = Math::mul($paidViaMoneyPart, $parent['parent_paid_renta_via_money']);

                // Добавяме частта от платената рента към родителя към вече платената рента на родители от по-горно ниво.
                $heritorPart = Math::add($paidHeritorPart, $parentPaidRentaPart);

                // Запазваме платената рента към текущия човек, за да може да я използваме за по-долните нива на дървото.
                $child['parent_paid_renta_via_money'] = $heritorPart;

                // Изваждаме платената рента към родителите от всички нива от дължимата рента на наследника.
                $child['unpaid_renta'] = Math::sub($child['unpaid_renta'], $heritorPart);
            }

            // Плащане на пари в натура
            if (null !== $parent['paid_via_nat']) {
                foreach ($parent['paid_via_nat'] as $paidViaNatKey => $paidViaNatValue) {
                    // Пресмята се платената сума на база на платената рента в натура и стойността на единицата на рента в натура.
                    $paidMoney = Math::mul($paidViaNatValue['nat_trans_amount'], $paidViaNatValue['nat_unit_value']);

                    // Намиране каква част от платената рента към родителя, ще прибавим към платената рента на наследника
                    $paidHeritorPart = Math::mul($paidMoney, $paidViaMoneyPart);

                    // Намиране каква част от платената рента към родителите от по-горни нива, ще прибавим към платената рента на наследника
                    $parentPaidRentaPart = Math::mul($paidViaMoneyPart, $parent['parent_paid_renta_via_nat']);

                    // Добавяме частта от платената рента към родителя към вече платената рента на родители от по-горно ниво.
                    $heritorPart = Math::add($paidHeritorPart, $parentPaidRentaPart);

                    // Запазваме платената рента към текущия човек, за да може да я използваме за по-долните нива на дървото.
                    $child['parent_paid_renta_via_nat'] = $heritorPart;

                    // Изваждаме платената рента към родителите от всички нива от дължимата рента на наследника.
                    $child['unpaid_renta'] = Math::sub($child['unpaid_renta'], $heritorPart);
                }
            }

            if (null !== $parent['paid_renta_nat_sum']) {
                foreach ($parent['paid_renta_nat_sum'] as $rentaType => $paidRentaValue) {
                    // Намира се съотношението между рентата на родителя и наследника, за да се използва при разпределянето на платената рента в натура към
                    // родителя към наследниците. Аналогично на платената рента в пари $paidViaMoneyPart.
                    $rentNatChild = $child['charged_renta_nat'][$rentaType] ?? $child['renta_nat'][$rentaType];
                    $rentNatParent = $parent['charged_renta_nat'][$rentaType] ?? $parent['renta_nat'][$rentaType];
                    $paidViaNatPart = Math::div($rentNatChild, $rentNatParent);

                    // Намиране каква част от платеното количество рента в натура към родителя, ще прибавим към платеното количество на наследника
                    $paidHeritorPart = Math::mul($paidRentaValue, $paidViaNatPart);

                    // Изваждаме платеното към родителите количество от всички нива от дължимата рента в натура на наследника.
                    $child['unpaid_renta_nat_arr'][$rentaType] = Math::sub($child['unpaid_renta_nat_arr'][$rentaType], $paidHeritorPart);

                    // Намираме стойността на платената рента в пари
                    $heritorPartValue = Math::mul($paidHeritorPart, $child['renta_nat_info'][$rentaType]['unit_value']);

                    // Изваеждаме стойността на платената рента в натура от масива за дължима рента в натура в паричен еквивалент
                    $child['unpaid_renta_nat_money_arr'][$rentaType] = Math::sub($child['unpaid_renta_nat_money_arr'][$rentaType], $heritorPartValue);

                    // Запазваме платената рента към текущия човек, за да може да я използваме за по-долните нива на дървото.
                    if (!isset($child['parent_paid_renta_nat_sum'][$rentaType])) {
                        $child['parent_paid_renta_nat_sum'][$rentaType] = 0;
                    }
                    $child['parent_paid_renta_nat_sum'][$rentaType] = Math::add($child['parent_paid_renta_nat_sum'][$rentaType], $paidHeritorPart);
                }
            }

            if (null !== $parent['parent_paid_renta_nat_sum']) {
                foreach ($parent['parent_paid_renta_nat_sum'] as $rentaType => $paidRentaValue) {
                    // Намира се съотношението между рентата на родителя и наследника, за да се използва при разпределянето на платената рента в натура към
                    // родителя към наследниците. Аналогично на платената рента в пари $paidViaMoneyPart.
                    $rentNatChild = $child['charged_renta_nat'][$rentaType] ?? $child['renta_nat'][$rentaType];
                    $rentNatParent = $parent['charged_renta_nat'][$rentaType] ?? $parent['renta_nat'][$rentaType];
                    $paidViaNatPart = Math::div($rentNatChild, $rentNatParent);

                    // Намиране каква част от платеното количество рента в натура към родителя, ще прибавим към платеното количество на наследника
                    $paidHeritorPart = Math::mul($paidRentaValue, $paidViaNatPart);

                    // Изваждаме платеното към родителите количество от всички нива от дължимата рента в натура на наследника.
                    $child['unpaid_renta_nat_arr'][$rentaType] = Math::sub($child['unpaid_renta_nat_arr'][$rentaType], $paidHeritorPart);

                    // Намираме стойността на платената рента в пари
                    $heritorPartValue = Math::mul($paidHeritorPart, $child['renta_nat_info'][$rentaType]['unit_value']);

                    // Изваеждаме стойността на платената рента в натура от масива за дължима рента в натура в паричен еквивалент
                    $child['unpaid_renta_nat_money_arr'][$rentaType] = Math::sub($child['unpaid_renta_nat_money_arr'][$rentaType], $heritorPartValue);

                    // Прехвърляме рентата платена от по-горните нива на дървото към по-долните
                    if (!isset($child['parent_paid_renta_nat_sum'][$rentaType])) {
                        $child['parent_paid_renta_nat_sum'][$rentaType] = 0;
                    }
                    $child['parent_paid_renta_nat_sum'][$rentaType] = Math::add($child['parent_paid_renta_nat_sum'][$rentaType], $heritorPart);
                }
            }

            if (!empty($child['children'])) {
                $this->calculateChildrenRentsBasedOfParentPayments($child['children'], $child);
            }
        }
    }

    private function calculateParentRentsBasedOfChildrenPayments(array &$ownersTree)
    {
        foreach ($ownersTree as &$owner) {
            if (!empty($owner['children'])) {
                $this->calculateParentRentsBasedOfChildrenPayments($owner['children']);

                $childrenPaidMoney = Math::round(0, $this->moneyPrecision);
                $childrenPaidNat = [];
                $hasChildrenPaids = false;
                foreach ($owner['children'] as $child) {
                    $childrenPaidRenta = Math::add($child['paid_renta'], $child['children_paid_renta']);
                    $childrenPaidMoney = Math::add($childrenPaidMoney, $childrenPaidRenta);
                    if (!$hasChildrenPaids) {
                        $hasChildrenPaids = $child['have_children_paids'];
                    }

                    foreach ($child['paid_renta_nat_sum'] as $rentaType => $paidRentaValue) {
                        $childrenPaidNat[$rentaType] = Math::add($childrenPaidNat[$rentaType], $paidRentaValue);
                    }

                    foreach ($child['children_paid_renta_nat'] as $rentaType => $childPaidRentaValue) {
                        $owner['unpaid_renta_nat_arr'][$rentaType] = Math::sub($owner['unpaid_renta_nat_arr'][$rentaType], $childPaidRentaValue);
                        $paidRentaMoneyValue = Math::mul($childPaidRentaValue, $child['renta_nat_info'][$rentaType]['unit_value']);
                        $owner['unpaid_renta_nat_money_arr'][$rentaType] = Math::sub($owner['unpaid_renta_nat_money_arr'][$rentaType], $paidRentaMoneyValue);

                        $owner['children_paid_renta_nat'][$rentaType] = Math::add($owner['children_paid_renta_nat'][$rentaType], $childPaidRentaValue);
                    }
                }

                $owner['children_paid_renta'] = Math::add($owner['children_paid_renta'], $childrenPaidMoney, $this->moneyPrecision);
                $owner['unpaid_renta'] = Math::sub($owner['unpaid_renta'], $childrenPaidMoney);

                if ($childrenPaidMoney > 0 || !empty($childrenPaidNat) || $hasChildrenPaids) {
                    $owner['have_children_paids'] = true;
                }

                if (!empty($childrenPaidNat)) {
                    foreach ($childrenPaidNat as $rentaType => $paidRentaValue) {
                        // Ако собственика няма за получаване рента в натура, но има платена за наследника, тя не се изважда от неговата дължимо количество
                        // така надплащането остава само за наследника, а не се прехвърля към собственика
                        if ($owner['unpaid_renta_nat_arr'][$rentaType] <= $paidRentaValue) {
                            $owner['unpaid_renta_nat_arr'][$rentaType] = 0;
                            $owner['unpaid_renta_nat_money_arr'][$rentaType] = 0;
                        } else {
                            $owner['unpaid_renta_nat_arr'][$rentaType] = Math::sub($owner['unpaid_renta_nat_arr'][$rentaType], $paidRentaValue);

                            $paidRentaMoneyValue = Math::mul($paidRentaValue, $owner['renta_nat_info'][$rentaType]['unit_value']);
                            $owner['unpaid_renta_nat_money_arr'][$rentaType] = Math::sub($owner['unpaid_renta_nat_money_arr'][$rentaType], $paidRentaMoneyValue);
                        }

                        $owner['children_paid_renta_nat'][$rentaType] = Math::add($owner['children_paid_renta_nat'][$rentaType], $paidRentaValue);
                    }
                }
            }
        }
    }

    private function calcFooter($data, $footerData = [], $isChild = false): array
    {
        if ([] == $footerData) {
            $footerData = [
                'all_owner_area' => 0.00,
                'pu_area' => 0.00,
                'owner_area' => 0.00,
                'renta' => 0.00,
                'charged_renta' => 0.00,
                'renta_nat_text' => 0.00,
                'charged_renta_nat_text' => 0.00,
                'paid_renta' => 0.00,
                'paid_via_money' => 0.00,
                'paid_renta_by' => 0.00,
                'paid_renta_nat' => [],
                'paid_renta_nat_by' => [],
                'paid_renta_nat_by_detailed' => [],
                'unpaid_renta' => 0.00,
                'unpaid_renta_nat' => [],
                'unpaid_renta_nat_unit_value' => [],
                'renta_nat_type' => [],
                'over_paid' => 0,
                'over_paid_nat' => [],
            ];
        }

        foreach ($data as $owner) {
            if (!$isChild) {
                $footerData['all_owner_area'] = Math::add($footerData['all_owner_area'], $owner['all_owner_area'], 3);
                $footerData['pu_area'] = Math::add($footerData['pu_area'], $owner['pu_area'], 3);
                $footerData['owner_area'] = Math::add($footerData['owner_area'], $owner['owner_area'], 3);
                $footerData['charged_renta'] = Math::add($footerData['charged_renta'], $owner['charged_renta'], 2);
                $footerData['renta'] = Math::add($footerData['renta'], $owner['renta'], 2);
                $footerData['unpaid_renta'] = Math::add($footerData['unpaid_renta'], $owner['unpaid_renta'], 2);

                foreach ($owner['renta_nat_info'] as $rentaNatId => $rentaNatValue) {
                    if (!isset($footerData['renta_nat_info'][$rentaNatId])) {
                        $footerData['renta_nat_info'][$rentaNatId] = $rentaNatValue;
                    }
                }

                if ($owner['renta_nat']) {
                    foreach ($owner['renta_nat'] as $rentaNatId => $rentaNatValue) {
                        if (!isset($footerData['renta_nat'][$rentaNatId])) {
                            $footerData['renta_nat'][$rentaNatId] = 0;
                        }
                        $footerData['renta_nat'][$rentaNatId] = Math::add($footerData['renta_nat'][$rentaNatId], $rentaNatValue);
                    }
                }

                if ($owner['charged_renta_nat']) {
                    foreach ($owner['charged_renta_nat'] as $rentaNatId => $rentaNatValue) {
                        if (!isset($footerData['charged_renta_nat'][$rentaNatId])) {
                            $footerData['charged_renta_nat'][$rentaNatId] = 0;
                        }
                        $footerData['charged_renta_nat'][$rentaNatId] = Math::add($footerData['charged_renta_nat'][$rentaNatId], $rentaNatValue);
                    }
                }

                foreach ($owner['unpaid_renta_nat_arr'] as $rentaNatId => $rentaNatValue) {
                    if (!isset($footerData['unpaid_renta_nat_arr'][$rentaNatId])) {
                        $footerData['unpaid_renta_nat_arr'][$rentaNatId] = 0;
                    }
                    $footerData['unpaid_renta_nat_arr'][$rentaNatId] = Math::add($footerData['unpaid_renta_nat_arr'][$rentaNatId], $rentaNatValue);

                    if (!isset($footerData['unpaid_renta_nat_money_arr'][$rentaNatId])) {
                        $footerData['unpaid_renta_nat_money_arr'][$rentaNatId] = 0;
                    }
                    $rentaNatMoneyValue = Math::mul($rentaNatValue, $owner['renta_nat_info'][$rentaNatId]['unit_value']);
                    $footerData['unpaid_renta_nat_money_arr'][$rentaNatId] = Math::add($footerData['unpaid_renta_nat_money_arr'][$rentaNatId], $rentaNatMoneyValue);
                }
            }

            if ($owner['paid_renta_nat_sum']) {
                foreach ($owner['paid_renta_nat_sum'] as $paidNatViaMoneyKey => $paidNatViaMoneyValue) {
                    if (!isset($footerData['paid_renta_nat_sum'][$paidNatViaMoneyKey])) {
                        $footerData['paid_renta_nat_sum'][$paidNatViaMoneyKey] = 0;
                    }
                    $footerData['paid_renta_nat_sum'][$paidNatViaMoneyKey] = Math::add($footerData['paid_renta_nat_sum'][$paidNatViaMoneyKey], $paidNatViaMoneyValue);
                }
            }

            if ($owner['paid_nat_via_money']) {
                foreach ($owner['paid_nat_via_money'] as $paidNatViaMoneyKey => $paidNatViaMoneyValue) {
                    if (!isset($footerData['paid_nat_via_money'][$paidNatViaMoneyKey])) {
                        $footerData['paid_nat_via_money'][$paidNatViaMoneyKey] = $paidNatViaMoneyValue;
                    }
                    $footerData['paid_nat_via_money'][$paidNatViaMoneyKey]['nat_trans_amount'] = Math::add($footerData['paid_nat_via_money'][['nat_trans_amount']][$paidNatViaMoneyKey], $paidNatViaMoneyValue['nat_trans_amount']);
                }
            }

            if ($owner['paid_nat_via_nat']) {
                foreach ($owner['paid_nat_via_nat'] as $paidNatViaMoneyKey => $paidNatViaMoneyValue) {
                    if (!isset($footerData['paid_nat_via_nat'][$paidNatViaMoneyKey])) {
                        $footerData['paid_nat_via_nat'][$paidNatViaMoneyKey] = $paidNatViaMoneyValue;
                    }
                    $footerData['paid_nat_via_nat'][$paidNatViaMoneyKey]['nat_trans_amount'] = Math::add($footerData['paid_nat_via_nat'][['nat_trans_amount']][$paidNatViaMoneyKey], $paidNatViaMoneyValue['nat_trans_amount']);
                }
            }

            if ($owner['paid_via_nat']) {
                foreach ($owner['paid_via_nat'] as $paidNatViaMoneyKey => $paidNatViaMoneyValue) {
                    if (!isset($footerData['paid_via_nat'][$paidNatViaMoneyKey])) {
                        $footerData['paid_via_nat'][$paidNatViaMoneyKey] = $paidNatViaMoneyValue;

                        continue;
                    }
                    $footerData['paid_via_nat'][$paidNatViaMoneyKey]['nat_trans_amount'] = Math::add($footerData['paid_via_nat'][['nat_trans_amount']][$paidNatViaMoneyKey], $paidNatViaMoneyValue['nat_trans_amount']);
                }
            }

            $footerData['paid_renta'] = Math::add($footerData['paid_renta'], $owner['paid_renta'], 2);
            $footerData['paid_via_money'] = Math::add($footerData['paid_via_money'], $owner['paid_via_money'], 2);
            $footerData['over_paid'] = Math::add($footerData['over_paid'], $owner['over_paid'], 2);

            if ($owner['children']) {
                $footerData = $this->calcFooter($owner['children'], $footerData, true);
            }
        }

        return $footerData;
    }

    private function generateFooter(array $data, $unsetColums = []): array
    {
        $footerData = $this->calcFooter($data);

        // Форматериране на данните на футъра. Форматъра работи с двумерни масиви и връща такива, затова от резултата се взима първия елемент
        $formatterValues = $this->formattingOwnersData([$footerData]);

        // Премахване на ненужните колони от футъра
        foreach ($unsetColums as $unsetColum) {
            unset($formatterValues[0][$unsetColum]);
        }

        return array_merge([
            'iconCls' => 'no-background',
            'owner_names' => '',
            'rep_names' => '<b>ОБЩО за стр.</b>',
        ], $formatterValues[0]);
    }

    /**
     * Get allowed farming IDs based on user permissions.
     *
     * @return array Array of allowed farming IDs
     */
    private function getAllowedFarmingIds()
    {
        $farmingController = new FarmingController('Farming');
        $userFarmings = $farmingController->getUserFarmings();

        // Validate that getUserFarmings() returns a valid array
        if (empty($userFarmings) || !is_array($userFarmings)) {
            // Return empty array as safe default when no farmings are available
            return [];
        }

        return array_keys($userFarmings);
    }
}
